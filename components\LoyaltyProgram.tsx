import { useMemo, type FC } from 'react';
import type { LoyaltyProgram } from '../types';
import Icon from './Icon';

interface LoyaltyProgramProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
}

// Mock loyalty data - in a real app, this would come from an API
const mockLoyaltyData: LoyaltyProgram[] = [
  {
    userId: 'user-1',
    points: 1250,
    tier: 'gold',
    totalSpent: 2450.75,
    joinDate: '2023-06-15T10:00:00Z'
  },
  {
    userId: 'user-2',
    points: 450,
    tier: 'silver',
    totalSpent: 890.50,
    joinDate: '2023-09-20T14:30:00Z'
  }
];

const tierInfo = {
  bronze: {
    name: 'Bronze',
    color: 'bg-amber-600',
    textColor: 'text-amber-600',
    bgColor: 'bg-amber-50',
    minSpent: 0,
    maxSpent: 499,
    pointsMultiplier: 1,
    benefits: ['1 point per $1 spent', 'Birthday discount', 'Member-only sales access']
  },
  silver: {
    name: 'Silver',
    color: 'bg-gray-400',
    textColor: 'text-gray-600',
    bgColor: 'bg-gray-50',
    minSpent: 500,
    maxSpent: 1499,
    pointsMultiplier: 1.25,
    benefits: ['1.25 points per $1 spent', 'Free shipping on orders over $75', 'Early access to sales', 'Birthday discount']
  },
  gold: {
    name: 'Gold',
    color: 'bg-yellow-500',
    textColor: 'text-yellow-600',
    bgColor: 'bg-yellow-50',
    minSpent: 1500,
    maxSpent: 2999,
    pointsMultiplier: 1.5,
    benefits: ['1.5 points per $1 spent', 'Free shipping on all orders', 'Priority customer service', 'Exclusive member events', 'Birthday discount']
  },
  platinum: {
    name: 'Platinum',
    color: 'bg-purple-600',
    textColor: 'text-purple-600',
    bgColor: 'bg-purple-50',
    minSpent: 3000,
    maxSpent: Infinity,
    pointsMultiplier: 2,
    benefits: ['2 points per $1 spent', 'Free shipping & returns', 'Personal stylist consultation', 'VIP customer service', 'Exclusive platinum events', 'Birthday discount']
  }
};

const pointsRewards = [
  { points: 100, reward: '$5 off your next order', value: 5 },
  { points: 250, reward: '$15 off your next order', value: 15 },
  { points: 500, reward: '$30 off your next order', value: 30 },
  { points: 750, reward: '$50 off your next order', value: 50 },
  { points: 1000, reward: '$75 off your next order', value: 75 },
  { points: 1500, reward: '$125 off your next order', value: 125 }
];

const getTierFromSpent = (totalSpent: number): keyof typeof tierInfo => {
  if (totalSpent >= 3000) return 'platinum';
  if (totalSpent >= 1500) return 'gold';
  if (totalSpent >= 500) return 'silver';
  return 'bronze';
};

const getNextTier = (currentTier: keyof typeof tierInfo): { tier: keyof typeof tierInfo; amountNeeded: number } | null => {
  const tiers: (keyof typeof tierInfo)[] = ['bronze', 'silver', 'gold', 'platinum'];
  const currentIndex = tiers.indexOf(currentTier);
  
  if (currentIndex === tiers.length - 1) return null; // Already at highest tier
  
  const nextTier = tiers[currentIndex + 1];
  const amountNeeded = tierInfo[nextTier].minSpent;
  
  return { tier: nextTier, amountNeeded };
};

const LoyaltyProgram: FC<LoyaltyProgramProps> = ({ isOpen, onClose, userId }) => {
  const loyaltyData = useMemo(() => {
    return mockLoyaltyData.find(data => data.userId === userId);
  }, [userId]);

  const currentTierInfo = useMemo(() => {
    if (!loyaltyData) return tierInfo.bronze;
    return tierInfo[loyaltyData.tier];
  }, [loyaltyData]);

  const nextTierInfo = useMemo(() => {
    if (!loyaltyData) return null;
    return getNextTier(loyaltyData.tier);
  }, [loyaltyData]);

  const availableRewards = useMemo(() => {
    if (!loyaltyData) return [];
    return pointsRewards.filter(reward => loyaltyData.points >= reward.points);
  }, [loyaltyData]);

  if (!isOpen) return null;

  if (!loyaltyData) {
    return (
      <div className="fixed inset-0 z-50 overflow-hidden">
        <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
        <div className="absolute inset-x-4 top-4 bottom-4 md:inset-x-auto md:left-1/2 md:transform md:-translate-x-1/2 md:w-full md:max-w-2xl bg-white rounded-lg shadow-2xl overflow-hidden">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900">Loyalty Program</h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
            >
              <Icon name="x-mark" className="w-6 h-6" />
            </button>
          </div>
          <div className="p-6 text-center">
            <Icon name="gift" className="w-16 h-16 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Join Our Loyalty Program</h3>
            <p className="text-gray-600 mb-6">
              Sign in to start earning points and unlock exclusive rewards!
            </p>
          </div>
        </div>
      </div>
    );
  }

  const progressToNextTier = nextTierInfo 
    ? ((loyaltyData.totalSpent - currentTierInfo.minSpent) / (nextTierInfo.amountNeeded - currentTierInfo.minSpent)) * 100
    : 100;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      <div className="absolute inset-x-4 top-4 bottom-4 md:inset-x-auto md:left-1/2 md:transform md:-translate-x-1/2 md:w-full md:max-w-4xl bg-white rounded-lg shadow-2xl overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">Loyalty Program</h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
          >
            <Icon name="x-mark" className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 overflow-auto max-h-[calc(100vh-200px)]">
          {/* Current Status */}
          <div className={`${currentTierInfo.bgColor} rounded-lg p-6 mb-6`}>
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className={`text-2xl font-bold ${currentTierInfo.textColor}`}>
                  {currentTierInfo.name} Member
                </h3>
                <p className="text-gray-600">
                  Member since {new Date(loyaltyData.joinDate).toLocaleDateString()}
                </p>
              </div>
              <div className="text-right">
                <p className="text-3xl font-bold text-gray-900">{loyaltyData.points}</p>
                <p className="text-sm text-gray-600">Points Available</p>
              </div>
            </div>

            {/* Progress to Next Tier */}
            {nextTierInfo && (
              <div>
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Progress to {tierInfo[nextTierInfo.tier].name}</span>
                  <span>${loyaltyData.totalSpent.toFixed(2)} / ${nextTierInfo.amountNeeded}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${tierInfo[nextTierInfo.tier].color}`}
                    style={{ width: `${Math.min(progressToNextTier, 100)}%` }}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Spend ${(nextTierInfo.amountNeeded - loyaltyData.totalSpent).toFixed(2)} more to reach {tierInfo[nextTierInfo.tier].name}
                </p>
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Available Rewards */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Available Rewards</h3>
              {availableRewards.length > 0 ? (
                <div className="space-y-3">
                  {availableRewards.map((reward, index) => (
                    <div key={index} className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-green-800">{reward.reward}</p>
                          <p className="text-sm text-green-600">{reward.points} points</p>
                        </div>
                        <button className="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">
                          Redeem
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Icon name="gift" className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                  <p>No rewards available yet</p>
                  <p className="text-sm">Keep shopping to earn more points!</p>
                </div>
              )}
            </div>

            {/* Current Tier Benefits */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Your {currentTierInfo.name} Benefits</h3>
              <div className="space-y-3">
                {currentTierInfo.benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <Icon name="check-circle" className="w-5 h-5 text-green-600 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* All Tiers Overview */}
          <div className="mt-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Tier Benefits</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {Object.entries(tierInfo).map(([tier, info]) => (
                <div 
                  key={tier}
                  className={`border rounded-lg p-4 ${
                    loyaltyData.tier === tier ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200'
                  }`}
                >
                  <h4 className={`font-semibold ${info.textColor} mb-2`}>{info.name}</h4>
                  <p className="text-sm text-gray-600 mb-3">
                    ${info.minSpent}+ spent
                  </p>
                  <ul className="text-xs text-gray-600 space-y-1">
                    <li>• {info.pointsMultiplier}x points</li>
                    <li>• {info.benefits.length} benefits</li>
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoyaltyProgram;
