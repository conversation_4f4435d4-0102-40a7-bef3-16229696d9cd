import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { ReactNode } from 'react';
import { CartProvider, useCart } from '../../context/CartContext';
import { AuthProvider } from '../../context/AuthContext';
import type { CartItem } from '../../types';

// Mock AuthContext
const mockAuthContext = {
  currentUser: null,
  login: vi.fn(),
  register: vi.fn(),
  logout: vi.fn(),
  isLoading: false,
};

vi.mock('../../context/AuthContext', async () => {
  const actual = await vi.importActual('../../context/AuthContext');
  return {
    ...actual,
    useAuth: () => mockAuthContext,
  };
});

const createWrapper = (initialUser = null) => {
  mockAuthContext.currentUser = initialUser;
  
  return ({ children }: { children: ReactNode }) => (
    <AuthProvider>
      <CartProvider>{children}</CartProvider>
    </AuthProvider>
  );
};

describe('CartContext', () => {
  const mockCartItem: CartItem = {
    productId: 'product-1',
    variantId: 'variant-1',
    quantity: 1,
    unitPrice: 29.99,
  };

  const mockCartItem2: CartItem = {
    productId: 'product-2',
    variantId: 'variant-2',
    quantity: 2,
    unitPrice: 19.99,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockAuthContext.currentUser = null;
  });

  it('throws error when useCart is used outside CartProvider', () => {
    expect(() => {
      renderHook(() => useCart());
    }).toThrow('useCart must be used within a CartProvider');
  });

  it('initializes with empty cart', () => {
    const { result } = renderHook(() => useCart(), {
      wrapper: createWrapper(),
    });

    expect(result.current.cartItems).toEqual([]);
    expect(result.current.totalCartItems).toBe(0);
    expect(result.current.getTotal()).toBe(0);
  });

  it('adds item to cart', () => {
    const { result } = renderHook(() => useCart(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.addToCart(mockCartItem);
    });

    expect(result.current.cartItems).toHaveLength(1);
    expect(result.current.cartItems[0]).toEqual(mockCartItem);
    expect(result.current.totalCartItems).toBe(1);
  });

  it('increases quantity when adding existing item', () => {
    const { result } = renderHook(() => useCart(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.addToCart(mockCartItem);
    });

    act(() => {
      result.current.addToCart({ ...mockCartItem, quantity: 2 });
    });

    expect(result.current.cartItems).toHaveLength(1);
    expect(result.current.cartItems[0].quantity).toBe(3);
    expect(result.current.totalCartItems).toBe(3);
  });

  it('adds multiple different items to cart', () => {
    const { result } = renderHook(() => useCart(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.addToCart(mockCartItem);
    });

    act(() => {
      result.current.addToCart(mockCartItem2);
    });

    expect(result.current.cartItems).toHaveLength(2);
    expect(result.current.totalCartItems).toBe(3); // 1 + 2
  });

  it('removes item from cart', () => {
    const { result } = renderHook(() => useCart(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.addToCart(mockCartItem);
      result.current.addToCart(mockCartItem2);
    });

    act(() => {
      result.current.removeFromCart('variant-1');
    });

    expect(result.current.cartItems).toHaveLength(1);
    expect(result.current.cartItems[0].variantId).toBe('variant-2');
    expect(result.current.totalCartItems).toBe(2);
  });

  it('updates item quantity', () => {
    const { result } = renderHook(() => useCart(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.addToCart(mockCartItem);
    });

    act(() => {
      result.current.updateQuantity('variant-1', 5);
    });

    expect(result.current.cartItems[0].quantity).toBe(5);
    expect(result.current.totalCartItems).toBe(5);
  });

  it('removes item when quantity is updated to 0', () => {
    const { result } = renderHook(() => useCart(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.addToCart(mockCartItem);
    });

    act(() => {
      result.current.updateQuantity('variant-1', 0);
    });

    expect(result.current.cartItems).toHaveLength(0);
    expect(result.current.totalCartItems).toBe(0);
  });

  it('removes item when quantity is updated to negative number', () => {
    const { result } = renderHook(() => useCart(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.addToCart(mockCartItem);
    });

    act(() => {
      result.current.updateQuantity('variant-1', -1);
    });

    expect(result.current.cartItems).toHaveLength(0);
    expect(result.current.totalCartItems).toBe(0);
  });

  it('clears entire cart', () => {
    const { result } = renderHook(() => useCart(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.addToCart(mockCartItem);
      result.current.addToCart(mockCartItem2);
    });

    act(() => {
      result.current.clearCart();
    });

    expect(result.current.cartItems).toHaveLength(0);
    expect(result.current.totalCartItems).toBe(0);
  });

  it('calculates total price correctly', () => {
    const { result } = renderHook(() => useCart(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.addToCart(mockCartItem); // 1 * 29.99 = 29.99
      result.current.addToCart(mockCartItem2); // 2 * 19.99 = 39.98
    });

    expect(result.current.getTotal()).toBe(69.97); // 29.99 + 39.98
  });

  it('clears cart when user logs out', () => {
    const mockUser = { id: 'user-1', name: 'Test User', email: '<EMAIL>', role: 'customer' as const };
    
    const { result, rerender } = renderHook(() => useCart(), {
      wrapper: createWrapper(mockUser),
    });

    act(() => {
      result.current.addToCart(mockCartItem);
    });

    expect(result.current.cartItems).toHaveLength(1);

    // Simulate logout
    mockAuthContext.currentUser = null;
    rerender();

    expect(result.current.cartItems).toHaveLength(0);
  });

  it('handles non-existent variant ID gracefully', () => {
    const { result } = renderHook(() => useCart(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.addToCart(mockCartItem);
    });

    // Try to remove non-existent item
    act(() => {
      result.current.removeFromCart('non-existent-variant');
    });

    expect(result.current.cartItems).toHaveLength(1);

    // Try to update non-existent item
    act(() => {
      result.current.updateQuantity('non-existent-variant', 5);
    });

    expect(result.current.cartItems).toHaveLength(1);
    expect(result.current.cartItems[0].quantity).toBe(1); // Unchanged
  });
});
