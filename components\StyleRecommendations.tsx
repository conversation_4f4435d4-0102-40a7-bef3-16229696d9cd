import { useState, useMemo, type FC } from 'react';
import { useAuth } from '../context/AuthContext';
import { products as allProducts, reviews as allReviews } from '../data';
import type { Product } from '../types';
import Icon from './Icon';

interface StyleRecommendationsProps {
  isOpen: boolean;
  onClose: () => void;
  onProductSelect: (id: string) => void;
}

interface StyleProfile {
  preferredCategories: string[];
  priceRange: [number, number];
  favoriteColors: string[];
  bodyType: 'slim' | 'regular' | 'athletic' | 'plus';
  lifestyle: 'professional' | 'casual' | 'active' | 'formal';
  age: 'under-25' | '25-35' | '35-45' | '45-55' | 'over-55';
}

interface OutfitSuggestion {
  id: string;
  name: string;
  occasion: string;
  products: Product[];
  totalPrice: number;
  description: string;
}

// Mock user style profiles - in a real app, this would be learned from user behavior
const mockStyleProfiles: Record<string, StyleProfile> = {
  'user-1': {
    preferredCategories: ['Suits', 'Casual'],
    priceRange: [100, 500],
    favoriteColors: ['Navy Blue', 'Charcoal Grey', 'White'],
    bodyType: 'regular',
    lifestyle: 'professional',
    age: '25-35'
  }
};

const generateOutfitSuggestions = (profile: StyleProfile): OutfitSuggestion[] => {
  const suitProducts = allProducts.filter(p => p.category === 'Suits');
  const casualProducts = allProducts.filter(p => p.category === 'Casual');
  const footwearProducts = allProducts.filter(p => p.category === 'Footwear');
  const accessoryProducts = allProducts.filter(p => p.category === 'Accessories');

  const outfits: OutfitSuggestion[] = [];

  // Professional Business Outfit
  if (profile.lifestyle === 'professional' && suitProducts.length > 0) {
    const suit = suitProducts[0];
    const shoes = footwearProducts.find(p => p.name.includes('Oxford')) || footwearProducts[0];
    const products = [suit, shoes].filter(Boolean);
    
    outfits.push({
      id: 'outfit-1',
      name: 'Executive Business Look',
      occasion: 'Business meetings, presentations',
      products,
      totalPrice: products.reduce((sum, p) => sum + p.basePrice, 0),
      description: 'A sharp, professional ensemble perfect for making a strong impression in the boardroom.'
    });
  }

  // Smart Casual Outfit
  if (casualProducts.length > 0) {
    const shirt = casualProducts.find(p => p.name.includes('Shirt')) || casualProducts[0];
    const shoes = footwearProducts.find(p => p.name.includes('Loafer')) || footwearProducts[0];
    const products = [shirt, shoes].filter(Boolean);
    
    outfits.push({
      id: 'outfit-2',
      name: 'Smart Casual Weekend',
      occasion: 'Casual meetings, weekend outings',
      products,
      totalPrice: products.reduce((sum, p) => sum + p.basePrice, 0),
      description: 'Effortlessly stylish for those occasions that call for polished yet relaxed attire.'
    });
  }

  // Date Night Outfit
  if (suitProducts.length > 0 && accessoryProducts.length > 0) {
    const suit = suitProducts.find(p => p.name.includes('Navy')) || suitProducts[0];
    const shoes = footwearProducts[0];
    const products = [suit, shoes].filter(Boolean);
    
    outfits.push({
      id: 'outfit-3',
      name: 'Date Night Elegance',
      occasion: 'Dinner dates, evening events',
      products,
      totalPrice: products.reduce((sum, p) => sum + p.basePrice, 0),
      description: 'Sophisticated and romantic, perfect for making a memorable impression on special evenings.'
    });
  }

  return outfits;
};

const getPersonalizedRecommendations = (profile: StyleProfile): Product[] => {
  return allProducts
    .filter(product => {
      // Filter by preferred categories
      if (!profile.preferredCategories.includes(product.category)) return false;
      
      // Filter by price range
      if (product.basePrice < profile.priceRange[0] || product.basePrice > profile.priceRange[1]) return false;
      
      // Filter by favorite colors
      const productColors = product.variants.map(v => v.color);
      if (!productColors.some(color => profile.favoriteColors.includes(color))) return false;
      
      return true;
    })
    .sort((a, b) => {
      // Sort by rating and reviews
      const aReviews = allReviews.filter(r => r.productId === a.id);
      const bReviews = allReviews.filter(r => r.productId === b.id);
      const aRating = aReviews.length > 0 ? aReviews.reduce((acc, r) => acc + r.rating, 0) / aReviews.length : 0;
      const bRating = bReviews.length > 0 ? bReviews.reduce((acc, r) => acc + r.rating, 0) / bReviews.length : 0;
      return bRating - aRating;
    })
    .slice(0, 6);
};

const StyleRecommendations: FC<StyleRecommendationsProps> = ({ isOpen, onClose, onProductSelect }) => {
  const { currentUser } = useAuth();
  const [activeTab, setActiveTab] = useState<'recommendations' | 'outfits'>('recommendations');

  const userProfile = useMemo(() => {
    if (!currentUser) return null;
    return mockStyleProfiles[currentUser.id];
  }, [currentUser]);

  const personalizedProducts = useMemo(() => {
    if (!userProfile) return [];
    return getPersonalizedRecommendations(userProfile);
  }, [userProfile]);

  const outfitSuggestions = useMemo(() => {
    if (!userProfile) return [];
    return generateOutfitSuggestions(userProfile);
  }, [userProfile]);

  if (!isOpen) return null;

  if (!currentUser || !userProfile) {
    return (
      <div className="fixed inset-0 z-50 overflow-hidden">
        <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
        <div className="absolute inset-x-4 top-4 bottom-4 md:inset-x-auto md:left-1/2 md:transform md:-translate-x-1/2 md:w-full md:max-w-2xl bg-white rounded-lg shadow-2xl overflow-hidden">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900">Style Recommendations</h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
            >
              <Icon name="x-mark" className="w-6 h-6" />
            </button>
          </div>
          <div className="p-6 text-center">
            <Icon name="sparkles" className="w-16 h-16 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Sign In for Personalized Recommendations</h3>
            <p className="text-gray-600">
              Create an account to get AI-powered style recommendations tailored just for you!
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      <div className="absolute inset-x-4 top-4 bottom-4 md:inset-x-auto md:left-1/2 md:transform md:-translate-x-1/2 md:w-full md:max-w-6xl bg-white rounded-lg shadow-2xl overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">Style Recommendations</h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
          >
            <Icon name="x-mark" className="w-6 h-6" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('recommendations')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'recommendations'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              For You
            </button>
            <button
              onClick={() => setActiveTab('outfits')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'outfits'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Complete Outfits
            </button>
          </nav>
        </div>

        <div className="p-6 overflow-auto max-h-[calc(100vh-250px)]">
          {activeTab === 'recommendations' ? (
            <div>
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Curated Just for You</h3>
                <p className="text-gray-600">
                  Based on your style preferences: {userProfile.lifestyle} lifestyle, 
                  {userProfile.favoriteColors.join(', ')} colors, ${userProfile.priceRange[0]}-${userProfile.priceRange[1]} budget
                </p>
              </div>
              
              {personalizedProducts.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {personalizedProducts.map((product) => (
                    <div
                      key={product.id}
                      onClick={() => onProductSelect(product.id)}
                      className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
                    >
                      <img
                        src={product.variants[0].imageUrl}
                        alt={product.name}
                        className="w-full h-48 object-cover"
                      />
                      <div className="p-4">
                        <h4 className="font-semibold text-gray-900 mb-1">{product.name}</h4>
                        <p className="text-gray-600 text-sm mb-2">{product.category}</p>
                        <p className="text-lg font-bold text-indigo-600">${product.basePrice.toFixed(2)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Icon name="sparkles" className="w-12 h-12 mx-auto text-gray-300 mb-4" />
                  <p className="text-gray-500">No recommendations available at the moment.</p>
                </div>
              )}
            </div>
          ) : (
            <div>
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Complete Outfit Ideas</h3>
                <p className="text-gray-600">
                  Professionally styled outfits for different occasions
                </p>
              </div>
              
              <div className="space-y-8">
                {outfitSuggestions.map((outfit) => (
                  <div key={outfit.id} className="bg-gray-50 rounded-lg p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h4 className="text-xl font-semibold text-gray-900">{outfit.name}</h4>
                        <p className="text-gray-600 text-sm">{outfit.occasion}</p>
                        <p className="text-gray-700 mt-2">{outfit.description}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-2xl font-bold text-indigo-600">${outfit.totalPrice.toFixed(2)}</p>
                        <p className="text-sm text-gray-500">Total outfit</p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {outfit.products.map((product) => (
                        <div
                          key={product.id}
                          onClick={() => onProductSelect(product.id)}
                          className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                        >
                          <img
                            src={product.variants[0].imageUrl}
                            alt={product.name}
                            className="w-full h-32 object-cover"
                          />
                          <div className="p-3">
                            <h5 className="font-medium text-gray-900 text-sm">{product.name}</h5>
                            <p className="text-indigo-600 font-semibold">${product.basePrice.toFixed(2)}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StyleRecommendations;
