import { useState, type FC } from 'react';
import type { PromoCode } from '../types';
import Icon from './Icon';

interface PromoCodeInputProps {
  onPromoApplied: (promoCode: PromoCode, discount: number) => void;
  onPromoRemoved: () => void;
  appliedPromo?: PromoCode;
  orderTotal: number;
}

// Mock promo codes - in a real app, this would come from an API
const mockPromoCodes: PromoCode[] = [
  {
    code: 'WELCOME10',
    discountType: 'percentage',
    discountValue: 10,
    minOrderAmount: 50,
    validFrom: '2024-01-01T00:00:00Z',
    validUntil: '2024-12-31T23:59:59Z',
    usageLimit: 1000,
    usedCount: 245,
    isActive: true
  },
  {
    code: 'SAVE20',
    discountType: 'fixed',
    discountValue: 20,
    minOrderAmount: 100,
    validFrom: '2024-01-01T00:00:00Z',
    validUntil: '2024-12-31T23:59:59Z',
    usageLimit: 500,
    usedCount: 123,
    isActive: true
  },
  {
    code: 'FREESHIP',
    discountType: 'fixed',
    discountValue: 15,
    minOrderAmount: 75,
    validFrom: '2024-01-01T00:00:00Z',
    validUntil: '2024-12-31T23:59:59Z',
    usageLimit: 2000,
    usedCount: 567,
    isActive: true
  },
  {
    code: 'BIGDEAL',
    discountType: 'percentage',
    discountValue: 25,
    minOrderAmount: 200,
    maxDiscount: 50,
    validFrom: '2024-01-01T00:00:00Z',
    validUntil: '2024-12-31T23:59:59Z',
    usageLimit: 100,
    usedCount: 89,
    isActive: true
  }
];

const calculateDiscount = (promoCode: PromoCode, orderTotal: number): number => {
  if (promoCode.discountType === 'percentage') {
    const discount = (orderTotal * promoCode.discountValue) / 100;
    return promoCode.maxDiscount ? Math.min(discount, promoCode.maxDiscount) : discount;
  } else {
    return promoCode.discountValue;
  }
};

const validatePromoCode = (code: string, orderTotal: number): { isValid: boolean; promoCode?: PromoCode; error?: string } => {
  const promoCode = mockPromoCodes.find(p => p.code.toLowerCase() === code.toLowerCase());
  
  if (!promoCode) {
    return { isValid: false, error: 'Invalid promo code' };
  }
  
  if (!promoCode.isActive) {
    return { isValid: false, error: 'This promo code is no longer active' };
  }
  
  const now = new Date();
  const validFrom = new Date(promoCode.validFrom);
  const validUntil = new Date(promoCode.validUntil);
  
  if (now < validFrom || now > validUntil) {
    return { isValid: false, error: 'This promo code has expired' };
  }
  
  if (promoCode.usageLimit && promoCode.usedCount >= promoCode.usageLimit) {
    return { isValid: false, error: 'This promo code has reached its usage limit' };
  }
  
  if (promoCode.minOrderAmount && orderTotal < promoCode.minOrderAmount) {
    return { isValid: false, error: `Minimum order amount of $${promoCode.minOrderAmount} required` };
  }
  
  return { isValid: true, promoCode };
};

const PromoCodeInput: FC<PromoCodeInputProps> = ({ 
  onPromoApplied, 
  onPromoRemoved, 
  appliedPromo, 
  orderTotal 
}) => {
  const [promoCode, setPromoCode] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleApplyPromo = async () => {
    if (!promoCode.trim()) {
      setError('Please enter a promo code');
      return;
    }

    setIsLoading(true);
    setError('');

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const validation = validatePromoCode(promoCode.trim(), orderTotal);
    
    if (validation.isValid && validation.promoCode) {
      const discount = calculateDiscount(validation.promoCode, orderTotal);
      onPromoApplied(validation.promoCode, discount);
      setPromoCode('');
    } else {
      setError(validation.error || 'Invalid promo code');
    }

    setIsLoading(false);
  };

  const handleRemovePromo = () => {
    onPromoRemoved();
    setError('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleApplyPromo();
    }
  };

  if (appliedPromo) {
    const discount = calculateDiscount(appliedPromo, orderTotal);
    
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Icon name="check-circle" className="w-5 h-5 text-green-600" />
            <div>
              <p className="text-sm font-medium text-green-800">
                Promo code "{appliedPromo.code}" applied
              </p>
              <p className="text-xs text-green-600">
                You saved ${discount.toFixed(2)}!
              </p>
            </div>
          </div>
          <button
            onClick={handleRemovePromo}
            className="text-green-600 hover:text-green-700 text-sm font-medium"
          >
            Remove
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="flex gap-2">
        <div className="flex-1">
          <input
            type="text"
            value={promoCode}
            onChange={(e) => {
              setPromoCode(e.target.value.toUpperCase());
              setError('');
            }}
            onKeyPress={handleKeyPress}
            placeholder="Enter promo code"
            className={`w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
              error ? 'border-red-300' : 'border-gray-300'
            }`}
            disabled={isLoading}
          />
        </div>
        <button
          onClick={handleApplyPromo}
          disabled={isLoading || !promoCode.trim()}
          className="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Applying...' : 'Apply'}
        </button>
      </div>
      
      {error && (
        <div className="flex items-center gap-2 text-red-600 text-sm">
          <Icon name="exclamation-circle" className="w-4 h-4" />
          <span>{error}</span>
        </div>
      )}

      {/* Available Promo Codes Hint */}
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Available Offers:</h4>
        <div className="space-y-1 text-xs text-gray-600">
          <p>• WELCOME10 - 10% off orders over $50</p>
          <p>• SAVE20 - $20 off orders over $100</p>
          <p>• FREESHIP - $15 off shipping on orders over $75</p>
          <p>• BIGDEAL - 25% off orders over $200 (max $50 off)</p>
        </div>
      </div>
    </div>
  );
};

export default PromoCodeInput;
