import { useMemo, type FC } from 'react';
import { products as allProducts, reviews as allReviews } from '../../data';
import Icon from '../Icon';

// Mock analytics data - in a real app, this would come from an API
const mockAnalyticsData = {
  salesData: [
    { month: 'Jan', revenue: 12500, orders: 45 },
    { month: 'Feb', revenue: 15200, orders: 52 },
    { month: 'Mar', revenue: 18900, orders: 68 },
    { month: 'Apr', revenue: 22100, orders: 75 },
    { month: 'May', revenue: 19800, orders: 71 },
    { month: 'Jun', revenue: 25600, orders: 89 }
  ],
  topProducts: [
    { productId: 'prod-1', sales: 45, revenue: 19124.55 },
    { productId: 'prod-2', sales: 38, revenue: 3039.62 },
    { productId: 'prod-3', sales: 32, revenue: 6399.68 },
    { productId: 'prod-4', sales: 28, revenue: 2799.72 }
  ],
  customerMetrics: {
    totalCustomers: 1250,
    newCustomers: 89,
    returningCustomers: 156,
    averageOrderValue: 285.50,
    customerLifetimeValue: 1420.75
  },
  trafficSources: [
    { source: 'Direct', visitors: 2450, percentage: 35 },
    { source: 'Google Search', visitors: 1890, percentage: 27 },
    { source: 'Social Media', visitors: 1260, percentage: 18 },
    { source: 'Email', visitors: 980, percentage: 14 },
    { source: 'Referrals', visitors: 420, percentage: 6 }
  ]
};

const Analytics: FC = () => {
  const analyticsStats = useMemo(() => {
    const totalRevenue = mockAnalyticsData.salesData.reduce((sum, month) => sum + month.revenue, 0);
    const totalOrders = mockAnalyticsData.salesData.reduce((sum, month) => sum + month.orders, 0);
    const averageOrderValue = totalRevenue / totalOrders;
    
    const currentMonth = mockAnalyticsData.salesData[mockAnalyticsData.salesData.length - 1];
    const previousMonth = mockAnalyticsData.salesData[mockAnalyticsData.salesData.length - 2];
    
    const revenueGrowth = ((currentMonth.revenue - previousMonth.revenue) / previousMonth.revenue) * 100;
    const orderGrowth = ((currentMonth.orders - previousMonth.orders) / previousMonth.orders) * 100;

    return {
      totalRevenue,
      totalOrders,
      averageOrderValue,
      revenueGrowth,
      orderGrowth,
      totalProducts: allProducts.length,
      totalReviews: allReviews.length
    };
  }, []);

  const getProductName = (productId: string) => {
    const product = allProducts.find(p => p.id === productId);
    return product?.name || 'Unknown Product';
  };

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Icon name="credit-card" className="w-8 h-8 text-green-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">${analyticsStats.totalRevenue.toLocaleString()}</div>
              <div className="text-sm text-gray-600">Total Revenue</div>
              <div className={`text-xs ${analyticsStats.revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {analyticsStats.revenueGrowth >= 0 ? '+' : ''}{analyticsStats.revenueGrowth.toFixed(1)}% from last month
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Icon name="shopping-bag" className="w-8 h-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">{analyticsStats.totalOrders}</div>
              <div className="text-sm text-gray-600">Total Orders</div>
              <div className={`text-xs ${analyticsStats.orderGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {analyticsStats.orderGrowth >= 0 ? '+' : ''}{analyticsStats.orderGrowth.toFixed(1)}% from last month
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Icon name="users" className="w-8 h-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">{mockAnalyticsData.customerMetrics.totalCustomers}</div>
              <div className="text-sm text-gray-600">Total Customers</div>
              <div className="text-xs text-green-600">+{mockAnalyticsData.customerMetrics.newCustomers} new this month</div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Icon name="chart-bar" className="w-8 h-8 text-indigo-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">${analyticsStats.averageOrderValue.toFixed(0)}</div>
              <div className="text-sm text-gray-600">Avg Order Value</div>
              <div className="text-xs text-gray-500">Customer LTV: ${mockAnalyticsData.customerMetrics.customerLifetimeValue}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Chart */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Trend</h3>
          <div className="space-y-3">
            {mockAnalyticsData.salesData.map((month, index) => {
              const maxRevenue = Math.max(...mockAnalyticsData.salesData.map(m => m.revenue));
              const width = (month.revenue / maxRevenue) * 100;
              
              return (
                <div key={month.month} className="flex items-center space-x-3">
                  <div className="w-8 text-sm text-gray-600">{month.month}</div>
                  <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
                    <div 
                      className="bg-indigo-600 h-6 rounded-full flex items-center justify-end pr-2"
                      style={{ width: `${width}%` }}
                    >
                      <span className="text-xs text-white font-medium">
                        ${month.revenue.toLocaleString()}
                      </span>
                    </div>
                  </div>
                  <div className="w-12 text-sm text-gray-600 text-right">{month.orders}</div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Selling Products</h3>
          <div className="space-y-4">
            {mockAnalyticsData.topProducts.map((item, index) => (
              <div key={item.productId} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-indigo-600">#{index + 1}</span>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">{getProductName(item.productId)}</div>
                    <div className="text-xs text-gray-500">{item.sales} units sold</div>
                  </div>
                </div>
                <div className="text-sm font-medium text-gray-900">
                  ${item.revenue.toLocaleString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Traffic Sources and Customer Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Traffic Sources */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Traffic Sources</h3>
          <div className="space-y-4">
            {mockAnalyticsData.trafficSources.map((source) => (
              <div key={source.source} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900">{source.source}</div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                      <div 
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${source.percentage}%` }}
                      />
                    </div>
                  </div>
                </div>
                <div className="ml-4 text-right">
                  <div className="text-sm font-medium text-gray-900">{source.visitors.toLocaleString()}</div>
                  <div className="text-xs text-gray-500">{source.percentage}%</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Customer Insights */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Insights</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <div>
                <div className="text-sm font-medium text-gray-900">New Customers</div>
                <div className="text-xs text-gray-500">This month</div>
              </div>
              <div className="text-lg font-bold text-green-600">
                {mockAnalyticsData.customerMetrics.newCustomers}
              </div>
            </div>
            
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <div>
                <div className="text-sm font-medium text-gray-900">Returning Customers</div>
                <div className="text-xs text-gray-500">This month</div>
              </div>
              <div className="text-lg font-bold text-blue-600">
                {mockAnalyticsData.customerMetrics.returningCustomers}
              </div>
            </div>
            
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <div>
                <div className="text-sm font-medium text-gray-900">Avg Order Value</div>
                <div className="text-xs text-gray-500">Per transaction</div>
              </div>
              <div className="text-lg font-bold text-purple-600">
                ${mockAnalyticsData.customerMetrics.averageOrderValue}
              </div>
            </div>
            
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <div>
                <div className="text-sm font-medium text-gray-900">Customer LTV</div>
                <div className="text-xs text-gray-500">Lifetime value</div>
              </div>
              <div className="text-lg font-bold text-indigo-600">
                ${mockAnalyticsData.customerMetrics.customerLifetimeValue}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Product Performance */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Performance Overview</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{analyticsStats.totalProducts}</div>
            <div className="text-sm text-gray-600">Total Products</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{analyticsStats.totalReviews}</div>
            <div className="text-sm text-gray-600">Total Reviews</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">
              {(analyticsStats.totalReviews / analyticsStats.totalProducts).toFixed(1)}
            </div>
            <div className="text-sm text-gray-600">Avg Reviews/Product</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">
              {allProducts.filter(p => p.offer).length}
            </div>
            <div className="text-sm text-gray-600">Products on Sale</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
