import { useMemo, type FC } from 'react';
import { useAuth } from '../context/AuthContext';
import { useWishlist } from '../context/WishlistContext';
import { products as allProducts, reviews as allReviews } from '../data';
import type { Product } from '../types';
import ProductCard from '../ProductCard';
import Icon from './Icon';

interface PersonalizedFeedProps {
  onProductSelect: (id: string) => void;
}

interface UserBehavior {
  viewedProducts: string[];
  wishlistedProducts: string[];
  purchasedProducts: string[];
  searchHistory: string[];
  categoryPreferences: Record<string, number>;
  priceRangePreference: [number, number];
}

// Mock user behavior data - in a real app, this would be tracked and stored
const mockUserBehavior: Record<string, UserBehavior> = {
  'user-1': {
    viewedProducts: ['prod-1', 'prod-3', 'prod-2'],
    wishlistedProducts: ['prod-1'],
    purchasedProducts: ['prod-2'],
    searchHistory: ['navy suit', 'oxford shoes', 'casual shirt'],
    categoryPreferences: { 'Suits': 0.4, 'Footwear': 0.3, 'Casual': 0.2, 'Accessories': 0.1 },
    priceRangePreference: [100, 600]
  },
  'user-2': {
    viewedProducts: ['prod-2', 'prod-4'],
    wishlistedProducts: ['prod-2'],
    purchasedProducts: [],
    searchHistory: ['casual wear', 'summer shirt'],
    categoryPreferences: { 'Casual': 0.6, 'Accessories': 0.2, 'Footwear': 0.1, 'Suits': 0.1 },
    priceRangePreference: [50, 200]
  }
};

const calculateProductScore = (product: Product, behavior: UserBehavior, wishlistedProducts: string[]): number => {
  let score = 0;

  // Base score from category preference
  score += (behavior.categoryPreferences[product.category] || 0) * 100;

  // Boost if in price range
  if (product.basePrice >= behavior.priceRangePreference[0] && product.basePrice <= behavior.priceRangePreference[1]) {
    score += 20;
  }

  // Boost if similar to viewed products
  const viewedCategories = behavior.viewedProducts.map(id => {
    const p = allProducts.find(prod => prod.id === id);
    return p?.category;
  }).filter(Boolean);
  
  if (viewedCategories.includes(product.category)) {
    score += 15;
  }

  // Boost if has good reviews
  const productReviews = allReviews.filter(r => r.productId === product.id);
  if (productReviews.length > 0) {
    const avgRating = productReviews.reduce((acc, r) => acc + r.rating, 0) / productReviews.length;
    score += avgRating * 5;
  }

  // Boost if on sale
  if (product.offer) {
    score += 10;
  }

  // Boost if wishlisted by user
  if (wishlistedProducts.includes(product.id)) {
    score += 30;
  }

  // Penalize if already purchased
  if (behavior.purchasedProducts.includes(product.id)) {
    score -= 50;
  }

  // Boost based on search history relevance
  const searchTerms = behavior.searchHistory.join(' ').toLowerCase();
  const productText = `${product.name} ${product.description} ${product.tags.join(' ')}`.toLowerCase();
  
  behavior.searchHistory.forEach(term => {
    if (productText.includes(term.toLowerCase())) {
      score += 10;
    }
  });

  return score;
};

const getRecommendationReason = (product: Product, behavior: UserBehavior, wishlistedProducts: string[]): string => {
  const reasons: string[] = [];

  if (wishlistedProducts.includes(product.id)) {
    return "In your wishlist";
  }

  if (product.offer) {
    reasons.push(`${product.offer.discountPercentage}% off`);
  }

  const categoryScore = behavior.categoryPreferences[product.category] || 0;
  if (categoryScore > 0.3) {
    reasons.push("Matches your style");
  }

  const productReviews = allReviews.filter(r => r.productId === product.id);
  if (productReviews.length > 0) {
    const avgRating = productReviews.reduce((acc, r) => acc + r.rating, 0) / productReviews.length;
    if (avgRating >= 4.5) {
      reasons.push("Highly rated");
    }
  }

  const viewedCategories = behavior.viewedProducts.map(id => {
    const p = allProducts.find(prod => prod.id === id);
    return p?.category;
  }).filter(Boolean);
  
  if (viewedCategories.includes(product.category)) {
    reasons.push("Similar to items you viewed");
  }

  if (product.basePrice >= behavior.priceRangePreference[0] && product.basePrice <= behavior.priceRangePreference[1]) {
    reasons.push("In your price range");
  }

  return reasons.length > 0 ? reasons[0] : "Recommended for you";
};

const PersonalizedFeed: FC<PersonalizedFeedProps> = ({ onProductSelect }) => {
  const { currentUser } = useAuth();
  const { wishlistItems } = useWishlist();

  const personalizedProducts = useMemo(() => {
    if (!currentUser) {
      // For non-logged-in users, show trending/popular products
      return allProducts
        .filter(product => {
          const reviews = allReviews.filter(r => r.productId === product.id);
          return reviews.length > 0;
        })
        .sort((a, b) => {
          const aReviews = allReviews.filter(r => r.productId === a.id);
          const bReviews = allReviews.filter(r => r.productId === b.id);
          const aRating = aReviews.reduce((acc, r) => acc + r.rating, 0) / aReviews.length;
          const bRating = bReviews.reduce((acc, r) => acc + r.rating, 0) / bReviews.length;
          return bRating - aRating;
        })
        .slice(0, 8);
    }

    const behavior = mockUserBehavior[currentUser.id];
    if (!behavior) {
      return allProducts.slice(0, 8);
    }

    // Calculate scores for all products
    const scoredProducts = allProducts
      .filter(product => !behavior.purchasedProducts.includes(product.id)) // Exclude purchased items
      .map(product => ({
        product,
        score: calculateProductScore(product, behavior, wishlistItems),
        reason: getRecommendationReason(product, behavior, wishlistItems)
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 8);

    return scoredProducts;
  }, [currentUser, wishlistItems]);

  const feedSections = useMemo(() => {
    if (!currentUser) {
      return [
        {
          title: "Trending Now",
          subtitle: "Popular items other customers love",
          products: personalizedProducts.map(p => ({ product: p, reason: "Trending" }))
        }
      ];
    }

    const behavior = mockUserBehavior[currentUser.id];
    if (!behavior) {
      return [
        {
          title: "Recommended for You",
          subtitle: "Curated selections based on your preferences",
          products: personalizedProducts.map(p => ({ product: p, reason: "Recommended" }))
        }
      ];
    }

    const sections = [];

    // Wishlist items
    const wishlistProducts = personalizedProducts.filter(item => 
      wishlistItems.includes(item.product.id)
    );
    if (wishlistProducts.length > 0) {
      sections.push({
        title: "From Your Wishlist",
        subtitle: "Items you've saved for later",
        products: wishlistProducts.slice(0, 4)
      });
    }

    // Sale items in preferred categories
    const saleProducts = personalizedProducts.filter(item => 
      item.product.offer && !wishlistItems.includes(item.product.id)
    );
    if (saleProducts.length > 0) {
      sections.push({
        title: "Special Offers for You",
        subtitle: "Great deals on items you'll love",
        products: saleProducts.slice(0, 4)
      });
    }

    // Recommended based on behavior
    const recommendedProducts = personalizedProducts.filter(item => 
      !item.product.offer && !wishlistItems.includes(item.product.id)
    );
    if (recommendedProducts.length > 0) {
      sections.push({
        title: "Recommended for You",
        subtitle: "Curated based on your style preferences",
        products: recommendedProducts.slice(0, 4)
      });
    }

    return sections;
  }, [currentUser, personalizedProducts, wishlistItems]);

  return (
    <div className="space-y-12">
      {feedSections.map((section, sectionIndex) => (
        <div key={sectionIndex}>
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900">{section.title}</h2>
            <p className="text-gray-600 mt-1">{section.subtitle}</p>
          </div>
          
          {section.products.length > 0 ? (
            <div className="grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 xl:gap-x-8">
              {section.products.map(({ product, reason }) => (
                <div key={product.id} className="relative">
                  <ProductCard product={product} onSelect={onProductSelect} />
                  <div className="absolute top-2 left-2 z-10">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                      {reason}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Icon name="sparkles" className="w-12 h-12 mx-auto text-gray-300 mb-4" />
              <p className="text-gray-500">No recommendations available in this section.</p>
            </div>
          )}
        </div>
      ))}
      
      {!currentUser && (
        <div className="bg-indigo-50 rounded-lg p-8 text-center">
          <Icon name="user" className="w-12 h-12 mx-auto text-indigo-600 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Get Personalized Recommendations</h3>
          <p className="text-gray-600 mb-4">
            Sign in to see products curated specifically for your style and preferences.
          </p>
          <button className="bg-indigo-600 text-white px-6 py-2 rounded-md hover:bg-indigo-700 transition-colors">
            Sign In
          </button>
        </div>
      )}
    </div>
  );
};

export default PersonalizedFeed;
