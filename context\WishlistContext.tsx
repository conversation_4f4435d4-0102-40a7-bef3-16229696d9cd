
import { createContext, useState, useContext, useCallback, useEffect, type ReactNode, type FC } from 'react';
import { useAuth } from './AuthContext';

type ProductId = string;

interface WishlistContextType {
    wishlistItems: ProductId[];
    addToWishlist: (productId: ProductId) => void;
    removeFromWishlist: (productId: ProductId) => void;
    isWishlisted: (productId: ProductId) => boolean;
    totalWishlistItems: number;
    clearWishlist: () => void;
}

const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

export const WishlistProvider: FC<{ children: ReactNode }> = ({ children }) => {
    const [wishlistItems, setWishlistItems] = useState<ProductId[]>([]);
    const { currentUser } = useAuth();

    const clearWishlist = useCallback(() => {
        setWishlistItems([]);
    }, []);
    
    // Clear wishlist on logout
    useEffect(() => {
        if (!currentUser) {
            clearWishlist();
        }
        // In a real app, you might fetch the user's persisted wishlist here upon login.
    }, [currentUser, clearWishlist]);

    const addToWishlist = useCallback((productId: ProductId) => {
        setWishlistItems(prevItems => [...new Set([...prevItems, productId])]);
    }, []);

    const removeFromWishlist = useCallback((productId: ProductId) => {
        setWishlistItems(prevItems => prevItems.filter(id => id !== productId));
    }, []);

    const isWishlisted = useCallback((productId: ProductId): boolean => {
        return wishlistItems.includes(productId);
    }, [wishlistItems]);

    const totalWishlistItems = wishlistItems.length;

    return (
        <WishlistContext.Provider value={{ wishlistItems, addToWishlist, removeFromWishlist, isWishlisted, totalWishlistItems, clearWishlist }}>
            {children}
        </WishlistContext.Provider>
    );
};

export const useWishlist = (): WishlistContextType => {
    const context = useContext(WishlistContext);
    if (context === undefined) {
        throw new Error('useWishlist must be used within a WishlistProvider');
    }
    return context;
};