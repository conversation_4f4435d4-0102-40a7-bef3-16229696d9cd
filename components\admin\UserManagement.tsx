import { useState, useMemo, type FC } from 'react';
import { users as allUsers } from '../../data';
import type { User } from '../../types';
import Icon from '../Icon';

// Mock additional user data - in a real app, this would come from an API
const mockUserStats = {
  'user-1': {
    totalOrders: 3,
    totalSpent: 784.97,
    lastOrderDate: '2024-01-18T14:20:00Z',
    joinDate: '2023-06-15T10:00:00Z',
    loyaltyPoints: 1250,
    isActive: true
  },
  'user-2': {
    totalOrders: 1,
    totalSpent: 199.99,
    lastOrderDate: '2024-01-10T09:15:00Z',
    joinDate: '2023-09-20T14:30:00Z',
    loyaltyPoints: 450,
    isActive: true
  },
  'user-3': {
    totalOrders: 0,
    totalSpent: 0,
    lastOrderDate: null,
    joinDate: '2024-01-01T12:00:00Z',
    loyaltyPoints: 0,
    isActive: true
  }
};

const UserManagement: FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRole, setSelectedRole] = useState<User['role'] | 'all'>('all');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const filteredUsers = useMemo(() => {
    let filtered = allUsers;

    if (selectedRole !== 'all') {
      filtered = filtered.filter(user => user.role === selectedRole);
    }

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(user => 
        user.name.toLowerCase().includes(query) ||
        user.email.toLowerCase().includes(query) ||
        user.id.toLowerCase().includes(query)
      );
    }

    return filtered.sort((a, b) => {
      const aStats = mockUserStats[a.id as keyof typeof mockUserStats];
      const bStats = mockUserStats[b.id as keyof typeof mockUserStats];
      
      if (!aStats || !bStats) return 0;
      
      return new Date(bStats.joinDate).getTime() - new Date(aStats.joinDate).getTime();
    });
  }, [selectedRole, searchQuery]);

  const userStats = useMemo(() => {
    const total = allUsers.length;
    const customers = allUsers.filter(u => u.role === 'customer').length;
    const sellers = allUsers.filter(u => u.role === 'seller').length;
    const admins = allUsers.filter(u => u.role === 'admin').length;
    const activeUsers = Object.values(mockUserStats).filter(s => s.isActive).length;
    const totalRevenue = Object.values(mockUserStats).reduce((sum, stats) => sum + stats.totalSpent, 0);

    return { total, customers, sellers, admins, activeUsers, totalRevenue };
  }, []);

  const updateUserRole = (userId: string, newRole: User['role']) => {
    // In a real app, this would make an API call
    console.log(`Updating user ${userId} to role: ${newRole}`);
  };

  const toggleUserStatus = (userId: string) => {
    // In a real app, this would make an API call
    console.log(`Toggling user ${userId} status`);
  };

  const getRoleBadgeColor = (role: User['role']) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800';
      case 'seller': return 'bg-blue-100 text-blue-800';
      case 'customer': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-2xl font-bold text-gray-900">{userStats.total}</div>
          <div className="text-sm text-gray-600">Total Users</div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-2xl font-bold text-green-600">{userStats.customers}</div>
          <div className="text-sm text-gray-600">Customers</div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-2xl font-bold text-blue-600">{userStats.sellers}</div>
          <div className="text-sm text-gray-600">Sellers</div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-2xl font-bold text-red-600">{userStats.admins}</div>
          <div className="text-sm text-gray-600">Admins</div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-2xl font-bold text-indigo-600">{userStats.activeUsers}</div>
          <div className="text-sm text-gray-600">Active</div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-2xl font-bold text-gray-900">${userStats.totalRevenue.toFixed(2)}</div>
          <div className="text-sm text-gray-600">Total Spent</div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search users by name, email, or ID..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          <select
            value={selectedRole}
            onChange={(e) => setSelectedRole(e.target.value as User['role'] | 'all')}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="all">All Roles</option>
            <option value="customer">Customers</option>
            <option value="seller">Sellers</option>
            <option value="admin">Admins</option>
          </select>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Join Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Spent</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredUsers.map((user) => {
                const stats = mockUserStats[user.id as keyof typeof mockUserStats];
                return (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-indigo-600 flex items-center justify-center">
                            <span className="text-sm font-medium text-white">
                              {user.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{user.name}</div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleBadgeColor(user.role)}`}>
                        {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {stats ? new Date(stats.joinDate).toLocaleDateString() : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {stats?.totalOrders || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      ${stats?.totalSpent.toFixed(2) || '0.00'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        stats?.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {stats?.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button
                        onClick={() => setSelectedUser(user)}
                        className="text-indigo-600 hover:text-indigo-900"
                      >
                        View
                      </button>
                      <select
                        value={user.role}
                        onChange={(e) => updateUserRole(user.id, e.target.value as User['role'])}
                        className="text-sm border border-gray-300 rounded px-2 py-1"
                      >
                        <option value="customer">Customer</option>
                        <option value="seller">Seller</option>
                        <option value="admin">Admin</option>
                      </select>
                      <button
                        onClick={() => toggleUserStatus(user.id)}
                        className={`px-2 py-1 text-xs rounded ${
                          stats?.isActive 
                            ? 'bg-red-100 text-red-800 hover:bg-red-200' 
                            : 'bg-green-100 text-green-800 hover:bg-green-200'
                        }`}
                      >
                        {stats?.isActive ? 'Deactivate' : 'Activate'}
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* User Detail Modal */}
      {selectedUser && (
        <div className="fixed inset-0 z-50 overflow-hidden">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setSelectedUser(null)} />
          <div className="absolute inset-x-4 top-4 bottom-4 md:inset-x-auto md:left-1/2 md:transform md:-translate-x-1/2 md:w-full md:max-w-2xl bg-white rounded-lg shadow-2xl overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-bold text-gray-900">User Details - {selectedUser.name}</h2>
              <button
                onClick={() => setSelectedUser(null)}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
              >
                <Icon name="x-mark" className="w-6 h-6" />
              </button>
            </div>
            <div className="p-6 overflow-auto max-h-[calc(100vh-200px)]">
              {(() => {
                const stats = mockUserStats[selectedUser.id as keyof typeof mockUserStats];
                return (
                  <div className="space-y-6">
                    <div className="flex items-center space-x-4">
                      <div className="h-16 w-16 rounded-full bg-indigo-600 flex items-center justify-center">
                        <span className="text-xl font-medium text-white">
                          {selectedUser.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">{selectedUser.name}</h3>
                        <p className="text-gray-600">{selectedUser.email}</p>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleBadgeColor(selectedUser.role)}`}>
                          {selectedUser.role.charAt(0).toUpperCase() + selectedUser.role.slice(1)}
                        </span>
                      </div>
                    </div>

                    {stats && (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <div className="text-2xl font-bold text-gray-900">{stats.totalOrders}</div>
                          <div className="text-sm text-gray-600">Total Orders</div>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <div className="text-2xl font-bold text-gray-900">${stats.totalSpent.toFixed(2)}</div>
                          <div className="text-sm text-gray-600">Total Spent</div>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <div className="text-2xl font-bold text-gray-900">{stats.loyaltyPoints}</div>
                          <div className="text-sm text-gray-600">Loyalty Points</div>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <div className="text-sm font-medium text-gray-900">
                            {stats.lastOrderDate ? new Date(stats.lastOrderDate).toLocaleDateString() : 'Never'}
                          </div>
                          <div className="text-sm text-gray-600">Last Order</div>
                        </div>
                      </div>
                    )}

                    <div>
                      <h4 className="text-lg font-medium text-gray-900 mb-2">Account Information</h4>
                      <div className="space-y-2 text-sm">
                        <div>
                          <span className="font-medium">User ID:</span> {selectedUser.id}
                        </div>
                        {stats && (
                          <>
                            <div>
                              <span className="font-medium">Join Date:</span> {new Date(stats.joinDate).toLocaleDateString()}
                            </div>
                            <div>
                              <span className="font-medium">Status:</span> 
                              <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                                stats.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                              }`}>
                                {stats.isActive ? 'Active' : 'Inactive'}
                              </span>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;
