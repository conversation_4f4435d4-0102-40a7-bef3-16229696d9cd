
import { useState, useCallback, type FC } from 'react';
import type { Product } from '../types';
import ProductCard from '../ProductCard';
import ProductFilters from './ProductFilters';
import PersonalizedFeed from './PersonalizedFeed';
import Icon from './Icon';

interface ProductListProps {
    products: Product[];
    onProductSelect: (id: string) => void;
    searchQuery?: string;
}

const ProductList: FC<ProductListProps> = ({ products, onProductSelect, searchQuery }) => {
    const hasSearchQuery = searchQuery && searchQuery.trim() !== '';
    const [filteredProducts, setFilteredProducts] = useState<Product[]>(products);
    const [isFiltersOpen, setIsFiltersOpen] = useState(false);

    const handleFilteredProductsChange = useCallback((filtered: Product[]) => {
        setFilteredProducts(filtered);
    }, []);

    // Show personalized feed when no search query and no filters applied
    const showPersonalizedFeed = !hasSearchQuery && filteredProducts.length === products.length;

    if (showPersonalizedFeed) {
        return <PersonalizedFeed onProductSelect={onProductSelect} />;
    }

    return (
        <div>
            {hasSearchQuery ? (
                 <h1 className="text-4xl font-extrabold tracking-tight text-gray-900 sm:text-5xl mb-8">
                    Results for "{searchQuery}"
                 </h1>
            ) : (
                <h1 className="text-4xl font-extrabold tracking-tight text-gray-900 sm:text-5xl mb-8">All Products</h1>
            )}

            {/* Filters and Results Count */}
            <div className="flex items-center justify-between mb-8">
                <div className="flex items-center gap-4">
                    <ProductFilters
                        products={products}
                        onFilteredProductsChange={handleFilteredProductsChange}
                        isOpen={isFiltersOpen}
                        onToggle={() => setIsFiltersOpen(!isFiltersOpen)}
                    />
                    <span className="text-sm text-gray-600">
                        {filteredProducts.length} {filteredProducts.length === 1 ? 'product' : 'products'} found
                    </span>
                </div>
            </div>

            {filteredProducts.length > 0 ? (
                <div className="grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 xl:gap-x-8">
                    {filteredProducts.map((product) => (
                        <ProductCard key={product.id} product={product} onSelect={onProductSelect} />
                    ))}
                </div>
            ) : (
                <div className="text-center py-20 px-4">
                    <Icon name="search" className="w-16 h-16 mx-auto text-gray-300"/>
                    <h3 className="mt-4 text-2xl font-bold text-gray-900">No products found</h3>
                    <p className="mt-2 text-md text-gray-500">
                        {hasSearchQuery
                            ? "We couldn't find any products matching your search. Try a different keyword or adjust your filters."
                            : "No products match your current filters. Try adjusting your filter criteria."
                        }
                    </p>
                </div>
            )}
        </div>
    );
};

export default ProductList;