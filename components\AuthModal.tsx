
import { useState, useCallback, type FormEvent, type FC } from 'react';
import { useAuth } from '../context/AuthContext';
import Icon from './Icon';

interface AuthModalProps {
    isOpen: boolean;
    onClose: () => void;
}

type AuthView = 'login' | 'register';

const AuthModal: FC<AuthModalProps> = ({ isOpen, onClose }) => {
    const [view, setView] = useState<AuthView>('login');
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [name, setName] = useState('');
    const [error, setError] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    
    const { login, register } = useAuth();

    const handleClose = useCallback(() => {
        if (isLoading) return;
        setError('');
        setName('');
        setEmail('');
        setPassword('');
        setView('login');
        onClose();
    }, [isLoading, onClose]);

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();
        setError('');
        setIsLoading(true);

        try {
            if (view === 'login') {
                await login(email, password);
            } else {
                await register(name, email, password);
            }
            handleClose();
        } catch (err) {
            if (err instanceof Error) {
                setError(err.message);
            } else {
                setError('An unexpected error occurred.');
            }
        } finally {
            setIsLoading(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="relative z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
            <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
                <div className="flex min-h-full items-center justify-center p-4 text-center sm:p-0">
                    <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-md">
                        <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                            <div className="sm:flex sm:items-start w-full">
                                <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                                    <div className="flex justify-between items-center">
                                         <h3 className="text-xl font-semibold leading-6 text-gray-900" id="modal-title">
                                            {view === 'login' ? 'Welcome Back' : 'Create Account'}
                                        </h3>
                                        <button onClick={handleClose} disabled={isLoading} className="text-gray-400 hover:text-gray-600 disabled:opacity-50">
                                            <Icon name="close" className="w-6 h-6"/>
                                        </button>
                                    </div>
                                   
                                    <div className="mt-4 border-b border-gray-200">
                                        <nav className="-mb-px flex space-x-6" aria-label="Tabs">
                                            <button onClick={() => setView('login')} className={`${view === 'login' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} whitespace-nowrap border-b-2 py-3 px-1 text-base font-medium`}>Login</button>
                                            <button onClick={() => setView('register')} className={`${view === 'register' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} whitespace-nowrap border-b-2 py-3 px-1 text-base font-medium`}>Sign Up</button>
                                        </nav>
                                    </div>

                                    <form onSubmit={handleSubmit} className="mt-6 space-y-4">
                                        {error && <p className="text-sm text-red-600 bg-red-100 p-3 rounded-md">{error}</p>}
                                        
                                        {view === 'register' && (
                                            <div>
                                                <label htmlFor="name" className="block text-sm font-medium text-gray-700">Name</label>
                                                <input type="text" name="name" id="name" value={name} onChange={e => setName(e.target.value)} required className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
                                            </div>
                                        )}
                                        <div>
                                            <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email address</label>
                                            <input type="email" name="email" id="email" value={email} onChange={e => setEmail(e.target.value)} required className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
                                        </div>
                                        <div>
                                            <label htmlFor="password" className="block text-sm font-medium text-gray-700">Password</label>
                                            <input type="password" name="password" id="password" value={password} onChange={e => setPassword(e.target.value)} required className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
                                            {view === 'login' && <p className="mt-1 text-xs text-gray-500">Hint: Any password works for this demo.</p>}
                                        </div>
                                        <button type="submit" disabled={isLoading} className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-indigo-300 disabled:cursor-not-allowed">
                                            {isLoading ? 'Processing...' : (view === 'login' ? 'Sign in' : 'Create Account')}
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AuthModal;