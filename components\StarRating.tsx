
import type { FC } from 'react';
import Icon from './Icon';

interface StarRatingProps {
    rating: number;
    onRatingChange?: (rating: number) => void;
    size?: 'sm' | 'md' | 'lg';
}

const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
};

const StarRating: FC<StarRatingProps> = ({ rating, onRatingChange, size = 'md' }) => {
    const isInteractive = !!onRatingChange;

    return (
        <div className={`flex items-center ${isInteractive ? 'gap-1' : ''}`}>
            {[...Array(5)].map((_, index) => {
                const starValue = index + 1;
                const star = (
                    <Icon
                        key={starValue}
                        name="star"
                        className={`${sizeClasses[size]} transition-colors duration-150 ${starValue <= rating ? 'text-yellow-400' : 'text-gray-300'} ${isInteractive ? 'hover:text-yellow-400' : ''}`}
                    />
                );

                if (isInteractive) {
                    return (
                        <button
                            type="button"
                            key={starValue}
                            onClick={() => onRatingChange(starValue)}
                            className="focus:outline-none"
                            aria-label={`Rate ${starValue} star${starValue > 1 ? 's' : ''}`}
                        >
                            {star}
                        </button>
                    );
                }

                return star;
            })}
        </div>
    );
};

export default StarRating;