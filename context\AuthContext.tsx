
import { createContext, useState, useContext, useCallback, useEffect, type ReactNode, type FC } from 'react';
import type { User } from '../types';
import { users as mockUsers } from '../data';

interface AuthContextType {
    currentUser: User | null;
    login: (email: string, password?: string) => Promise<User>;
    logout: () => void;
    register: (name: string, email: string, password?: string) => Promise<User>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const USER_STORAGE_KEY = 'marcat-user';

export const AuthProvider: FC<{ children: ReactNode }> = ({ children }) => {
    const [currentUser, setCurrentUser] = useState<User | null>(() => {
        try {
            const storedUser = localStorage.getItem(USER_STORAGE_KEY);
            return storedUser ? JSON.parse(storedUser) : null;
        } catch (error) {
            console.error("Failed to parse user from localStorage", error);
            return null;
        }
    });

    useEffect(() => {
        if (currentUser) {
            localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(currentUser));
        } else {
            localStorage.removeItem(USER_STORAGE_KEY);
        }
    }, [currentUser]);

    const login = useCallback(async (email: string, password?: string): Promise<User> => {
        // In a real app, you would make an API call to your backend.
        // Here we simulate it with mock data and a delay.
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                const user = mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase());
                if (user) {
                    setCurrentUser(user);
                    resolve(user);
                } else {
                    reject(new Error('User not found.'));
                }
            }, 500);
        });
    }, []);

    const logout = useCallback(() => {
        setCurrentUser(null);
    }, []);

    const register = useCallback(async (name: string, email: string, password?: string): Promise<User> => {
         return new Promise((resolve, reject) => {
            setTimeout(() => {
                const existingUser = mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase());
                if (existingUser) {
                    reject(new Error('An account with this email already exists.'));
                    return;
                }
                const newUser: User = {
                    id: `user-${Date.now()}`,
                    name,
                    email,
                    role: 'customer'
                };
                // In real app, you'd add this to the DB. Here we just add to the mock array.
                mockUsers.push(newUser);
                setCurrentUser(newUser);
                resolve(newUser);
            }, 500);
        });
    }, []);

    return (
        <AuthContext.Provider value={{ currentUser, login, logout, register }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = (): AuthContextType => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};