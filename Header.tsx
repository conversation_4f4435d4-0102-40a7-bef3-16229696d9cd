
import { useState, useRef, useEffect, type FC } from 'react';
import { useCart } from './context/CartContext';
import { useWishlist } from './context/WishlistContext';
import { useComparison } from './context/ComparisonContext';
import { useAuth } from './context/AuthContext';
import Icon from './Icon';

interface HeaderProps {
    onCartClick: () => void;
    onWishlistClick: () => void;
    onUserClick: () => void;
    onVisualSearchClick: () => void;
    onComparisonClick: () => void;
    onOrderTrackingClick: () => void;
    onLoyaltyClick: () => void;
    onAdminClick: () => void;
    categories: string[];
    activeCategory: string;
    onCategoryChange: (category: string) => void;
    onLogoClick: () => void;
    searchQuery: string;
    onSearchChange: (query: string) => void;
}

const UserMenu: FC<{ onOrderTrackingClick: () => void; onLoyaltyClick: () => void }> = ({ onOrderTrackingClick, onLoyaltyClick }) => {
    const { currentUser, logout } = useAuth();
    const [isMenuOpen, setMenuOpen] = useState(false);
    const menuRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                setMenuOpen(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);
    
    if (!currentUser) return null;

    const initials = currentUser.name.split(' ').map(n => n[0]).join('').toUpperCase();

    return (
        <div className="relative" ref={menuRef}>
            <button onClick={() => setMenuOpen(!isMenuOpen)} className="flex items-center justify-center w-10 h-10 bg-indigo-600 text-white rounded-full font-bold text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                {initials}
            </button>
            {isMenuOpen && (
                 <div className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                    <div className="px-4 py-3">
                        <p className="text-sm text-gray-900 font-semibold truncate">{currentUser.name}</p>
                        <p className="text-sm text-gray-500 truncate">{currentUser.email}</p>
                    </div>
                    <div className="border-t border-gray-100"></div>
                    <button
                        onClick={() => {
                            onOrderTrackingClick();
                            setMenuOpen(false);
                        }}
                        className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                    >
                        Order History
                    </button>
                    <button
                        onClick={() => {
                            onLoyaltyClick();
                            setMenuOpen(false);
                        }}
                        className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                    >
                        Loyalty Program
                    </button>
                    <div className="border-t border-gray-100"></div>
                     <button onClick={logout} className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                         Logout
                     </button>
                 </div>
            )}
        </div>
    );
}


const Header: FC<HeaderProps> = ({
    onCartClick,
    onWishlistClick,
    onUserClick,
    onVisualSearchClick,
    onComparisonClick,
    onOrderTrackingClick,
    onLoyaltyClick,
    onAdminClick,
    categories,
    activeCategory,
    onCategoryChange,
    onLogoClick,
    searchQuery,
    onSearchChange,
}) => {
    const { totalCartItems } = useCart();
    const { totalWishlistItems } = useWishlist();
    const { comparisonCount } = useComparison();
    const { currentUser } = useAuth();
    const [isSearchActive, setSearchActive] = useState(false);

    const handleSearchClose = () => {
        setSearchActive(false);
        onSearchChange('');
    };

    const handleLogoClick = () => {
        if (isSearchActive) {
            setSearchActive(false);
        }
        onLogoClick();
    };

    return (
        <header className="bg-white/80 backdrop-blur-lg sticky top-0 z-40 shadow-sm">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex items-center justify-between h-20 transition-all duration-300">
                    {isSearchActive ? (
                        <div className="flex items-center w-full gap-4">
                            <Icon name="search" className="w-6 h-6 text-gray-500 flex-shrink-0" />
                            <input
                                type="text"
                                placeholder="Search for products, brands, and more..."
                                className="w-full bg-transparent focus:outline-none text-lg text-gray-800"
                                value={searchQuery}
                                onChange={(e) => onSearchChange(e.target.value)}
                                autoFocus
                            />
                            <button onClick={handleSearchClose} className="text-gray-500 hover:text-gray-900">
                                <Icon name="close" className="w-7 h-7" />
                            </button>
                        </div>
                    ) : (
                        <>
                            {/* Logo */}
                            <div className="flex-shrink-0">
                                <button onClick={handleLogoClick} className="text-3xl font-extrabold tracking-tight text-gray-900">
                                    Marcat
                                </button>
                            </div>

                            {/* Navigation */}
                            <nav className="hidden md:flex items-center space-x-8">
                                {categories.map((category) => (
                                     <button
                                        key={category}
                                        onClick={() => onCategoryChange(category)}
                                        className={`text-lg font-medium transition-colors duration-200 ${
                                            activeCategory === category
                                                ? 'text-indigo-600 border-b-2 border-indigo-600'
                                                : 'text-gray-500 hover:text-gray-900'
                                        }`}
                                    >
                                        {category}
                                    </button>
                                ))}
                            </nav>

                            {/* Actions */}
                            <div className="flex items-center space-x-6">
                                <button onClick={() => setSearchActive(true)} className="text-gray-500 hover:text-gray-900 transition-colors" aria-label="Text Search">
                                    <Icon name="search" className="w-7 h-7" />
                                </button>
                                <button onClick={onVisualSearchClick} className="text-gray-500 hover:text-gray-900 transition-colors" aria-label="Visual Search">
                                    <Icon name="camera" className="w-7 h-7" />
                                </button>
                                <button onClick={onComparisonClick} className="relative text-gray-500 hover:text-gray-900 transition-colors" aria-label="Product Comparison">
                                    <Icon name="scale" className="w-7 h-7" />
                                    {comparisonCount > 0 && (
                                        <span className="absolute -top-2 -right-2 flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-indigo-600 rounded-full">
                                            {comparisonCount}
                                        </span>
                                    )}
                                </button>
                                 {currentUser?.role === 'admin' && (
                                    <button onClick={onAdminClick} className="text-gray-500 hover:text-gray-900 transition-colors" aria-label="Admin Dashboard">
                                        <Icon name="layout-grid" className="w-7 h-7" />
                                    </button>
                                )}
                                <button onClick={onWishlistClick} className="relative text-gray-500 hover:text-gray-900 transition-colors" aria-label="Wishlist">
                                    <Icon name="heart" className="w-7 h-7" />
                                     {totalWishlistItems > 0 && (
                                        <span className="absolute -top-2 -right-2 flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-600 rounded-full">
                                            {totalWishlistItems}
                                        </span>
                                    )}
                                </button>
                                
                                {currentUser ? (
                                    <UserMenu
                                        onOrderTrackingClick={onOrderTrackingClick}
                                        onLoyaltyClick={onLoyaltyClick}
                                    />
                                ) : (
                                    <button onClick={onUserClick} className="text-gray-500 hover:text-gray-900 transition-colors">
                                        <Icon name="user" className="w-7 h-7" />
                                    </button>
                                )}

                                <button onClick={onCartClick} className="relative text-gray-500 hover:text-gray-900 transition-colors" aria-label="Shopping Cart">
                                    <Icon name="cart" className="w-7 h-7" />
                                    {totalCartItems > 0 && (
                                        <span className="absolute -top-2 -right-2 flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-600 rounded-full">
                                            {totalCartItems}
                                        </span>
                                    )}
                                </button>
                            </div>
                        </>
                    )}
                </div>
            </div>
        </header>
    );
};

export default Header;
