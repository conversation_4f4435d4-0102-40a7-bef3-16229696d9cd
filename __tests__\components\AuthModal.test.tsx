import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../utils/test-utils';
import AuthModal from '../../components/AuthModal';
import * as AuthContext from '../../context/AuthContext';

const mockAuthContext = {
  currentUser: null,
  login: vi.fn(),
  register: vi.fn(),
  logout: vi.fn(),
  isLoading: false,
};

vi.spyOn(AuthContext, 'useAuth').mockReturnValue(mockAuthContext);

describe('AuthModal', () => {
  const mockOnClose = vi.fn();
  const user = userEvent.setup();

  const defaultProps = {
    isOpen: true,
    onClose: mockOnClose,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('does not render when isOpen is false', () => {
    render(<AuthModal {...defaultProps} isOpen={false} />);
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  it('renders login form by default', () => {
    render(<AuthModal {...defaultProps} />);

    expect(screen.getByText('Sign In')).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
    expect(screen.getByText("Don't have an account?")).toBeInTheDocument();
  });

  it('switches to register form when clicking register link', async () => {
    render(<AuthModal {...defaultProps} />);

    const registerLink = screen.getByText('Sign up');
    await user.click(registerLink);

    expect(screen.getByText('Create Account')).toBeInTheDocument();
    expect(screen.getByLabelText(/name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument();
  });

  it('switches back to login form when clicking login link', async () => {
    render(<AuthModal {...defaultProps} />);

    // Switch to register first
    await user.click(screen.getByText('Sign up'));
    expect(screen.getByText('Create Account')).toBeInTheDocument();

    // Switch back to login
    await user.click(screen.getByText('Sign in'));
    expect(screen.getByText('Sign In')).toBeInTheDocument();
  });

  it('handles login form submission', async () => {
    mockAuthContext.login.mockResolvedValue(undefined);
    render(<AuthModal {...defaultProps} />);

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');
    await user.click(screen.getByRole('button', { name: /sign in/i }));

    expect(mockAuthContext.login).toHaveBeenCalledWith('<EMAIL>', 'password123');
  });

  it('handles register form submission', async () => {
    mockAuthContext.register.mockResolvedValue(undefined);
    render(<AuthModal {...defaultProps} />);

    // Switch to register form
    await user.click(screen.getByText('Sign up'));

    await user.type(screen.getByLabelText(/name/i), 'John Doe');
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');
    await user.click(screen.getByRole('button', { name: /create account/i }));

    expect(mockAuthContext.register).toHaveBeenCalledWith('John Doe', '<EMAIL>', 'password123');
  });

  it('displays error message when login fails', async () => {
    const errorMessage = 'Invalid credentials';
    mockAuthContext.login.mockRejectedValue(new Error(errorMessage));
    render(<AuthModal {...defaultProps} />);

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'wrongpassword');
    await user.click(screen.getByRole('button', { name: /sign in/i }));

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  it('displays error message when registration fails', async () => {
    const errorMessage = 'Email already exists';
    mockAuthContext.register.mockRejectedValue(new Error(errorMessage));
    render(<AuthModal {...defaultProps} />);

    // Switch to register form
    await user.click(screen.getByText('Sign up'));

    await user.type(screen.getByLabelText(/name/i), 'John Doe');
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');
    await user.click(screen.getByRole('button', { name: /create account/i }));

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  it('shows loading state during form submission', async () => {
    // Mock a delayed login
    mockAuthContext.login.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    render(<AuthModal {...defaultProps} />);

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');
    
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);

    expect(submitButton).toBeDisabled();
    expect(screen.getByText('Signing in...')).toBeInTheDocument();
  });

  it('closes modal on successful login', async () => {
    mockAuthContext.login.mockResolvedValue(undefined);
    render(<AuthModal {...defaultProps} />);

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');
    await user.click(screen.getByRole('button', { name: /sign in/i }));

    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  it('closes modal when clicking close button', async () => {
    render(<AuthModal {...defaultProps} />);

    const closeButton = screen.getByLabelText('Close');
    await user.click(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('prevents closing modal during loading', async () => {
    mockAuthContext.login.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    render(<AuthModal {...defaultProps} />);

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');
    await user.click(screen.getByRole('button', { name: /sign in/i }));

    // Try to close during loading
    const closeButton = screen.getByLabelText('Close');
    await user.click(closeButton);

    // Should not close
    expect(mockOnClose).not.toHaveBeenCalled();
  });

  it('resets form when modal is closed and reopened', async () => {
    const { rerender } = render(<AuthModal {...defaultProps} />);

    // Fill form
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');

    // Close modal
    rerender(<AuthModal {...defaultProps} isOpen={false} />);
    
    // Reopen modal
    rerender(<AuthModal {...defaultProps} isOpen={true} />);

    // Form should be reset
    expect(screen.getByLabelText(/email/i)).toHaveValue('');
    expect(screen.getByLabelText(/password/i)).toHaveValue('');
    expect(screen.getByText('Sign In')).toBeInTheDocument(); // Should be back to login view
  });

  it('handles unexpected errors gracefully', async () => {
    mockAuthContext.login.mockRejectedValue('Unexpected error');
    render(<AuthModal {...defaultProps} />);

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');
    await user.click(screen.getByRole('button', { name: /sign in/i }));

    await waitFor(() => {
      expect(screen.getByText('An unexpected error occurred.')).toBeInTheDocument();
    });
  });
});
