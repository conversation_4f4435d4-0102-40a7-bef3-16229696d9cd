
import { useState, useMemo, type FormEvent, type FC } from 'react';
import type { Review } from '../types';
import { reviews as allReviews, users } from '../data';
import { useAuth } from '../context/AuthContext';
import StarRating from './StarRating';
import Icon from './Icon';
import ReviewSummary from './ReviewSummary';

interface ReviewsProps {
    productId: string;
}

const ReviewItem: FC<{ review: Review }> = ({ review }) => {
    const user = useMemo(() => users.find(u => u.id === review.userId), [review.userId]);
    const initials = user?.name.split(' ').map(n => n[0]).join('').toUpperCase() || '??';

    return (
        <div className="flex space-x-4 py-6">
            <div className="flex-shrink-0">
                <div className="flex items-center justify-center w-12 h-12 bg-indigo-100 text-indigo-600 rounded-full font-bold">
                    {initials}
                </div>
            </div>
            <div className="flex-1">
                <div className="flex items-center justify-between">
                    <div>
                        <p className="font-semibold text-gray-900">{user?.name || 'Anonymous'}</p>
                        <p className="text-sm text-gray-500">{new Date(review.createdAt).toLocaleDateString()}</p>
                    </div>
                    <StarRating rating={review.rating} />
                </div>
                <p className="mt-3 text-gray-700">{review.comment}</p>
            </div>
        </div>
    );
};


const Reviews: FC<ReviewsProps> = ({ productId }) => {
    const { currentUser } = useAuth();
    const [productReviews, setProductReviews] = useState<Review[]>(() => 
        allReviews
            .filter(r => r.productId === productId)
            .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    );
    const [newRating, setNewRating] = useState(0);
    const [newComment, setNewComment] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    const averageRating = useMemo(() => {
        if (productReviews.length === 0) return 0;
        const total = productReviews.reduce((acc, r) => acc + r.rating, 0);
        return total / productReviews.length;
    }, [productReviews]);
    
    const handleReviewSubmit = (e: FormEvent) => {
        e.preventDefault();
        if (!currentUser || newRating === 0 || !newComment.trim()) return;
        
        setIsSubmitting(true);
        // Simulate API call
        setTimeout(() => {
            const newReview: Review = {
                id: `rev-${Date.now()}`,
                productId,
                userId: currentUser.id,
                rating: newRating,
                comment: newComment,
                createdAt: new Date().toISOString(),
            };
            // In a real app, this would be returned from the API call
            allReviews.push(newReview);
            setProductReviews(prev => [newReview, ...prev]);
            
            setNewRating(0);
            setNewComment('');
            setIsSubmitting(false);
        }, 500);
    };

    return (
        <section>
            <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold tracking-tight text-gray-900">Customer Reviews</h2>
                <div className="flex items-center">
                    <StarRating rating={averageRating} size="lg" />
                    <p className="ml-2 text-gray-600">{averageRating.toFixed(1)} out of 5</p>
                    <p className="ml-4 text-sm text-gray-500">({productReviews.length} reviews)</p>
                </div>
            </div>

            {productReviews.length > 2 && (
                <div className="my-8">
                    <ReviewSummary productId={productId} />
                </div>
            )}
            
            {currentUser && (
                 <div className="mt-8 p-6 bg-gray-50 rounded-lg">
                    <form onSubmit={handleReviewSubmit}>
                        <h3 className="text-lg font-medium text-gray-900">Write a review</h3>
                        <div className="mt-4">
                            <label className="block text-sm font-medium text-gray-700">Your Rating</label>
                            <div className="mt-1">
                                <StarRating rating={newRating} onRatingChange={setNewRating} size="lg" />
                            </div>
                        </div>
                        <div className="mt-4">
                            <label htmlFor="comment" className="block text-sm font-medium text-gray-700">Your Review</label>
                            <textarea
                                id="comment"
                                name="comment"
                                rows={4}
                                value={newComment}
                                onChange={(e) => setNewComment(e.target.value)}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                placeholder="Share your thoughts on this product..."
                                required
                            />
                        </div>
                        <div className="mt-4 text-right">
                             <button type="submit" disabled={isSubmitting || newRating === 0} className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:bg-gray-400">
                                {isSubmitting ? 'Submitting...' : 'Submit Review'}
                            </button>
                        </div>
                    </form>
                </div>
            )}
            
            <div className="mt-8 divide-y divide-gray-200">
                {productReviews.length > 0 ? (
                    productReviews.map(review => <ReviewItem key={review.id} review={review} />)
                ) : (
                    <div className="text-center py-16">
                         <Icon name="star" className="w-16 h-16 mx-auto text-gray-300"/>
                        <h3 className="mt-4 text-xl font-bold text-gray-900">No reviews yet</h3>
                        <p className="mt-2 text-md text-gray-500">Be the first to share your thoughts on this product.</p>
                    </div>
                )}
            </div>
        </section>
    );
};

export default Reviews;
