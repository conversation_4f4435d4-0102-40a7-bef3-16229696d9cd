
import { createContext, useState, useContext, useCallback, useEffect, type ReactNode, type FC } from 'react';
import type { CartItem } from '../types';
import { useAuth } from './AuthContext';

interface CartContextType {
    cartItems: CartItem[];
    addToCart: (item: CartItem) => void;
    removeFromCart: (variantId: string) => void;
    updateQuantity: (variantId: string, quantity: number) => void;
    clearCart: () => void;
    totalCartItems: number;
    getTotal: () => number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider: FC<{ children: ReactNode }> = ({ children }) => {
    const [cartItems, setCartItems] = useState<CartItem[]>([]);
    const { currentUser } = useAuth();

    const clearCart = useCallback(() => {
        setCartItems([]);
    }, []);

    // Clear cart on logout
    useEffect(() => {
        if (!currentUser) {
            clearCart();
        }
        // In a real app, you might fetch the user's persisted cart here upon login.
    }, [currentUser, clearCart]);

    const addToCart = useCallback((newItem: CartItem) => {
        setCartItems(prevItems => {
            const existingItem = prevItems.find(item => item.variantId === newItem.variantId);
            if (existingItem) {
                return prevItems.map(item =>
                    item.variantId === newItem.variantId
                        ? { ...item, quantity: item.quantity + newItem.quantity }
                        : item
                );
            }
            return [...prevItems, newItem];
        });
    }, []);

    const removeFromCart = useCallback((variantId: string) => {
        setCartItems(prevItems => prevItems.filter(item => item.variantId !== variantId));
    }, []);

    const updateQuantity = useCallback((variantId: string, quantity: number) => {
        if (quantity <= 0) {
            removeFromCart(variantId);
        } else {
            setCartItems(prevItems =>
                prevItems.map(item =>
                    item.variantId === variantId ? { ...item, quantity } : item
                )
            );
        }
    }, [removeFromCart]);

    const totalCartItems = cartItems.reduce((total, item) => total + item.quantity, 0);

    const getTotal = useCallback(() => {
        return cartItems.reduce((total, item) => total + (item.unitPrice * item.quantity), 0);
    }, [cartItems]);

    return (
        <CartContext.Provider value={{ cartItems, addToCart, removeFromCart, updateQuantity, clearCart, totalCartItems, getTotal }}>
            {children}
        </CartContext.Provider>
    );
};

export const useCart = (): CartContextType => {
    const context = useContext(CartContext);
    if (context === undefined) {
        throw new Error('useCart must be used within a CartProvider');
    }
    return context;
};