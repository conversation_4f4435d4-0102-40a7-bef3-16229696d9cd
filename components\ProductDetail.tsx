
import { useState, useMemo, type FC } from 'react';
import type { Product, ProductVariant } from '../types';
import { stores, reviews as allReviews } from '../data';
import { useCart } from '../context/CartContext';
import { useWishlist } from '../context/WishlistContext';
import Icon from './Icon';
import StarRating from './StarRating';
import Reviews from './Reviews';
import StyleAssistant from './StyleAssistant';
import SizeGuide from './SizeGuide';

interface ProductDetailProps {
    product: Product;
    onBack: () => void;
    onCartAdd: () => void;
    onProductSelect: (id: string) => void;
}

const getDiscountedPrice = (price: number, discountPercentage?: number): number => {
    if (!discountPercentage) return price;
    return price * (1 - discountPercentage / 100);
};

const ProductDetail: FC<ProductDetailProps> = ({ product, onBack, onCartAdd, onProductSelect }) => {
    const { addToCart } = useCart();
    const { isWishlisted, addToWishlist, removeFromWishlist } = useWishlist();

    const colors = useMemo(() => [...new Map(product.variants.map(v => [v.color, v])).values()], [product.variants]);
    const [selectedColor, setSelectedColor] = useState<ProductVariant>(colors[0]);
    const [isSizeGuideOpen, setSizeGuideOpen] = useState(false);

    const availableSizes = useMemo(() => 
        product.variants.filter(v => v.color === selectedColor.color),
        [product.variants, selectedColor]
    );
    const [selectedVariant, setSelectedVariant] = useState<ProductVariant>(availableSizes[0]);
    
    const [quantity, setQuantity] = useState(1);
    const [mainImage, setMainImage] = useState(selectedVariant.imageUrl);

    const isProductWishlisted = isWishlisted(product.id);

    const reviewInfo = useMemo(() => {
        const productReviews = allReviews.filter(r => r.productId === product.id);
        if (productReviews.length === 0) {
            return { averageRating: 0, reviewCount: 0 };
        }
        const totalRating = productReviews.reduce((acc, review) => acc + review.rating, 0);
        return {
            averageRating: totalRating / productReviews.length,
            reviewCount: productReviews.length
        };
    }, [product.id]);

    const handleWishlistToggle = () => {
        if (isProductWishlisted) {
            removeFromWishlist(product.id);
        } else {
            addToWishlist(product.id);
        }
    };

    const handleColorSelect = (colorVariant: ProductVariant) => {
        setSelectedColor(colorVariant);
        const newSizes = product.variants.filter(v => v.color === colorVariant.color);
        const newSelectedVariant = newSizes[0] || product.variants[0];
        setSelectedVariant(newSelectedVariant);
        setMainImage(newSelectedVariant.imageUrl);
        setQuantity(1);
    };

    const handleSizeSelect = (size: string) => {
        const newVariant = availableSizes.find(v => v.size === size);
        if (newVariant) {
            setSelectedVariant(newVariant);
            setQuantity(1);
        }
    };
    
    const handleAddToCart = () => {
        const discountedPrice = getDiscountedPrice(product.basePrice, product.offer?.discountPercentage);
        addToCart({
            productId: product.id,
            variantId: selectedVariant.id,
            quantity: quantity,
            unitPrice: discountedPrice,
        });
        onCartAdd();
    };

    const discountedPrice = getDiscountedPrice(product.basePrice, product.offer?.discountPercentage);

    return (
        <div>
            <div className="bg-white rounded-lg shadow-2xl p-4 sm:p-8 lg:p-12">
                <button onClick={onBack} className="mb-8 inline-flex items-center gap-2 text-gray-600 hover:text-gray-900 font-semibold">
                    <Icon name="arrow-left" className="w-5 h-5"/>
                    Back to Products
                </button>
                <div className="lg:grid lg:grid-cols-2 lg:gap-x-12">
                    {/* Image gallery */}
                    <div className="flex flex-col-reverse">
                        <div className="mx-auto mt-6 hidden w-full max-w-2xl sm:block lg:max-w-none">
                            <ul className="grid grid-cols-4 gap-6">
                                {colors.map((variant) => (
                                    <li key={variant.id} onClick={() => handleColorSelect(variant)} className={`relative flex h-24 cursor-pointer items-center justify-center rounded-md bg-white text-sm font-medium uppercase text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring focus:ring-opacity-50 focus:ring-offset-4 ${mainImage === variant.imageUrl ? 'ring-2 ring-indigo-500 ring-offset-2' : 'ring-1 ring-gray-300'}`}>
                                        <span className="absolute inset-0 overflow-hidden rounded-md">
                                            <img src={variant.imageUrl} alt="" className="h-full w-full object-cover object-center" />
                                        </span>
                                    </li>
                                ))}
                            </ul>
                        </div>
                        <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-lg">
                            <img src={mainImage} alt={product.name} className="h-full w-full object-cover object-center" />
                        </div>
                    </div>

                    {/* Product info */}
                    <div className="mt-10 px-4 sm:mt-16 sm:px-0 lg:mt-0">
                        <h1 className="text-3xl font-bold tracking-tight text-gray-900">{product.name}</h1>
                        <div className="mt-3">
                            <p className="text-3xl tracking-tight text-gray-900">${discountedPrice.toFixed(2)}</p>
                            {product.offer && <p className="text-lg text-gray-500 line-through mt-1">${product.basePrice.toFixed(2)}</p>}
                        </div>

                        <div className="mt-3 flex items-center">
                            <StarRating rating={reviewInfo.averageRating} />
                            <p className="ml-2 text-sm text-gray-500">{reviewInfo.reviewCount} reviews</p>
                        </div>

                        <div className="mt-6">
                            <h3 className="sr-only">Description</h3>
                            <div className="space-y-6 text-base text-gray-700" dangerouslySetInnerHTML={{ __html: product.description }} />
                        </div>

                        <div className="mt-6">
                            {/* Colors */}
                            <div>
                                <h3 className="text-sm font-medium text-gray-900">Color</h3>
                                <div className="flex items-center space-x-3 mt-2">
                                    {colors.map(color => (
                                        <button key={color.id} onClick={() => handleColorSelect(color)} className={`relative -m-0.5 flex cursor-pointer items-center justify-center rounded-full p-0.5 focus:outline-none ${selectedColor.id === color.id ? 'ring ring-indigo-500 ring-offset-1' : ''}`}>
                                            <span style={{ backgroundColor: color.colorHex }} className="h-8 w-8 rounded-full border border-black border-opacity-10"></span>
                                        </button>
                                    ))}
                                </div>
                            </div>

                            {/* Sizes */}
                            <div className="mt-6">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-sm font-medium text-gray-900">Size</h3>
                                    <button
                                        onClick={() => setSizeGuideOpen(true)}
                                        className="text-sm text-indigo-600 hover:text-indigo-700 font-medium"
                                    >
                                        Size Guide
                                    </button>
                                </div>
                                <div className="grid grid-cols-4 gap-4 mt-2 sm:grid-cols-8 lg:grid-cols-4">
                                {availableSizes.map(variant => (
                                    <button key={variant.id} onClick={() => handleSizeSelect(variant.size)} disabled={variant.stock === 0} className={`group relative flex items-center justify-center rounded-md border py-3 px-4 text-sm font-medium uppercase hover:bg-gray-50 focus:outline-none sm:flex-1 ${variant.stock === 0 ? 'cursor-not-allowed bg-gray-50 text-gray-200' : 'bg-white text-gray-900 shadow-sm'} ${selectedVariant.id === variant.id ? 'ring-2 ring-indigo-500' : ''}`}>
                                        {variant.size}
                                        {variant.stock === 0 && <span aria-hidden="true" className="pointer-events-none absolute -inset-px rounded-md border-2 border-gray-200"><svg className="absolute inset-0 h-full w-full stroke-2 text-gray-200" viewBox="0 0 100 100" preserveAspectRatio="none" stroke="currentColor"><line x1="0" y1="100" x2="100" y2="0" vectorEffect="non-scaling-stroke"></line></svg></span>}
                                    </button>
                                ))}
                                </div>
                            </div>

                            {/* Quantity */}
                            <div className="mt-6">
                                <h3 className="text-sm font-medium text-gray-900">Quantity</h3>
                                <div className="flex items-center border border-gray-300 rounded-md w-32 mt-2">
                                    <button onClick={() => setQuantity(q => Math.max(1, q-1))} className="p-2 text-gray-600 hover:bg-gray-100 rounded-l-md"><Icon name="minus" className="w-5 h-5"/></button>
                                    <span className="flex-1 text-center font-semibold">{quantity}</span>
                                    <button onClick={() => setQuantity(q => Math.min(selectedVariant.stock, q+1))} className="p-2 text-gray-600 hover:bg-gray-100 rounded-r-md"><Icon name="plus" className="w-5 h-5"/></button>
                                </div>
                                <p className="text-sm text-gray-500 mt-1">{selectedVariant.stock} in stock</p>
                            </div>

                            <div className="mt-10 flex gap-4">
                                <button onClick={handleAddToCart} disabled={selectedVariant.stock === 0} className="flex-1 flex w-full items-center justify-center rounded-md border border-transparent bg-indigo-600 py-4 px-8 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed">
                                    Add to bag
                                </button>
                                <button
                                    type="button"
                                    onClick={handleWishlistToggle}
                                    className="flex items-center justify-center rounded-md p-4 border bg-white text-gray-400 hover:bg-gray-50 hover:text-red-500 transition-colors duration-200"
                                    aria-label={isProductWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
                                >
                                    <Icon name="heart" className={`h-7 w-7 transition-all ${isProductWishlisted ? 'text-red-500 fill-current' : ''}`} />
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="mt-12">
                <Reviews productId={product.id} />
            </div>

            <StyleAssistant product={product} onProductSelect={onProductSelect} />

            <SizeGuide
                isOpen={isSizeGuideOpen}
                onClose={() => setSizeGuideOpen(false)}
                category={product.category}
            />
        </div>
    );
};

export default ProductDetail;