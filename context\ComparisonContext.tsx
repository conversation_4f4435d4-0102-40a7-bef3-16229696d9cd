import { createContext, useContext, useState, useCallback, type FC, type ReactNode } from 'react';
import type { ComparisonItem } from '../types';

interface ComparisonContextType {
  comparisonItems: ComparisonItem[];
  addToComparison: (productId: string) => void;
  removeFromComparison: (productId: string) => void;
  clearComparison: () => void;
  isInComparison: (productId: string) => boolean;
  comparisonCount: number;
  maxComparisonItems: number;
}

const ComparisonContext = createContext<ComparisonContextType | undefined>(undefined);

export const useComparison = (): ComparisonContextType => {
  const context = useContext(ComparisonContext);
  if (!context) {
    throw new Error('useComparison must be used within a ComparisonProvider');
  }
  return context;
};

interface ComparisonProviderProps {
  children: ReactNode;
}

export const ComparisonProvider: FC<ComparisonProviderProps> = ({ children }) => {
  const [comparisonItems, setComparisonItems] = useState<ComparisonItem[]>([]);
  const maxComparisonItems = 4; // Maximum items that can be compared at once

  const addToComparison = useCallback((productId: string) => {
    setComparisonItems(prev => {
      // Check if already in comparison
      if (prev.some(item => item.productId === productId)) {
        return prev;
      }
      
      // Check if we've reached the maximum
      if (prev.length >= maxComparisonItems) {
        // Remove the oldest item and add the new one
        return [
          ...prev.slice(1),
          { productId, addedAt: new Date().toISOString() }
        ];
      }
      
      // Add new item
      return [
        ...prev,
        { productId, addedAt: new Date().toISOString() }
      ];
    });
  }, [maxComparisonItems]);

  const removeFromComparison = useCallback((productId: string) => {
    setComparisonItems(prev => prev.filter(item => item.productId !== productId));
  }, []);

  const clearComparison = useCallback(() => {
    setComparisonItems([]);
  }, []);

  const isInComparison = useCallback((productId: string) => {
    return comparisonItems.some(item => item.productId === productId);
  }, [comparisonItems]);

  const comparisonCount = comparisonItems.length;

  const value: ComparisonContextType = {
    comparisonItems,
    addToComparison,
    removeFromComparison,
    clearComparison,
    isInComparison,
    comparisonCount,
    maxComparisonItems,
  };

  return (
    <ComparisonContext.Provider value={value}>
      {children}
    </ComparisonContext.Provider>
  );
};
