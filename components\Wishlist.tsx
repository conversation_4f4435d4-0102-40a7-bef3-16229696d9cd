
import { useMemo, type FC } from 'react';
import { useWishlist } from '../context/WishlistContext';
import { useCart } from '../context/CartContext';
import { products as allProducts } from '../data';
import type { Product } from '../types';
import Icon from './Icon';

interface WishlistProps {
    isOpen: boolean;
    onClose: () => void;
    onProductSelect: (id: string) => void;
}

const getDiscountedPrice = (price: number, discountPercentage?: number): number => {
    if (!discountPercentage) return price;
    return price * (1 - discountPercentage / 100);
};

const WishlistItem: FC<{ product: Product, onRemove: () => void, onAddToCart: () => void, onSelect: () => void }> = ({ product, onRemove, onAddToCart, onSelect }) => {
    const discountedPrice = getDiscountedPrice(product.basePrice, product.offer?.discountPercentage);

    const handleAddToCart = (e: React.MouseEvent) => {
        e.stopPropagation();
        onAddToCart();
    };

    const handleRemove = (e: React.MouseEvent) => {
        e.stopPropagation();
        onRemove();
    };

    return (
        <li className="flex py-6 cursor-pointer hover:bg-gray-50 rounded-lg p-2 -m-2" onClick={onSelect}>
            <div className="h-28 w-28 flex-shrink-0 overflow-hidden rounded-md border border-gray-200">
                <img src={product.variants[0].imageUrl} alt={product.name} className="h-full w-full object-cover object-center" />
            </div>

            <div className="ml-4 flex flex-1 flex-col">
                <div>
                    <div className="flex justify-between text-base font-medium text-gray-900">
                        <h3>{product.name}</h3>
                        <p className="ml-4">${discountedPrice.toFixed(2)}</p>
                    </div>
                    <p className="mt-1 text-sm text-gray-500">{product.category}</p>
                </div>
                <div className="flex flex-1 items-end justify-between text-sm">
                    <button type="button" onClick={handleAddToCart} className="font-medium text-indigo-600 hover:text-indigo-500 flex items-center gap-1">
                        <Icon name="cart" className="w-4 h-4"/> Add to cart
                    </button>
                    <div className="flex">
                        <button type="button" onClick={handleRemove} className="font-medium text-gray-500 hover:text-red-500 flex items-center gap-1">
                            <Icon name="trash" className="w-4 h-4"/> Remove
                        </button>
                    </div>
                </div>
            </div>
        </li>
    )
}

const Wishlist: FC<WishlistProps> = ({ isOpen, onClose, onProductSelect }) => {
    const { wishlistItems, removeFromWishlist } = useWishlist();
    const { addToCart } = useCart();

    const wishlistDetails = useMemo(() => {
        return wishlistItems.map(productId => {
            return allProducts.find(p => p.id === productId);
        }).filter((p): p is Product => !!p);
    }, [wishlistItems]);

    const handleAddToCart = (product: Product) => {
        const discountedPrice = getDiscountedPrice(product.basePrice, product.offer?.discountPercentage);
        addToCart({
            productId: product.id,
            // Add the first variant by default. A real app might need a variant selector.
            variantId: product.variants[0].id,
            quantity: 1,
            unitPrice: discountedPrice,
        });
        alert(`${product.name} added to cart!`);
    }

    if (!isOpen) return null;

    return (
        <div className="relative z-50" aria-labelledby="slide-over-title" role="dialog" aria-modal="true">
            <div className={`fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity ${isOpen ? 'ease-in-out duration-500 opacity-100' : 'ease-in-out duration-500 opacity-0'}`}></div>

            <div className="fixed inset-0 overflow-hidden">
                <div className="absolute inset-0 overflow-hidden">
                    <div className={`pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10 transform transition ease-in-out duration-500 sm:duration-700 ${isOpen ? 'translate-x-0' : 'translate-x-full'}`}>
                        <div className="pointer-events-auto w-screen max-w-md">
                            <div className="flex h-full flex-col overflow-y-scroll bg-white shadow-xl">
                                <div className="flex-1 overflow-y-auto py-6 px-4 sm:px-6">
                                    <div className="flex items-start justify-between">
                                        <h2 className="text-lg font-medium text-gray-900" id="slide-over-title">My Wishlist</h2>
                                        <div className="ml-3 flex h-7 items-center">
                                            <button type="button" className="-m-2 p-2 text-gray-400 hover:text-gray-500" onClick={onClose}>
                                                <span className="sr-only">Close panel</span>
                                                <Icon name="close" className="h-6 w-6"/>
                                            </button>
                                        </div>
                                    </div>

                                    <div className="mt-8">
                                        <div className="flow-root">
                                            {wishlistDetails.length > 0 ? (
                                            <ul role="list" className="-my-6 divide-y divide-gray-200">
                                                {wishlistDetails.map((product) => (
                                                    <WishlistItem 
                                                        key={product.id}
                                                        product={product}
                                                        onRemove={() => removeFromWishlist(product.id)}
                                                        onAddToCart={() => handleAddToCart(product)}
                                                        onSelect={() => onProductSelect(product.id)}
                                                    />
                                                ))}
                                            </ul>
                                            ) : (
                                                <div className="text-center py-20">
                                                    <Icon name="heart" className="w-16 h-16 mx-auto text-gray-300"/>
                                                    <h3 className="mt-2 text-lg font-medium text-gray-900">Your wishlist is empty</h3>
                                                    <p className="mt-1 text-sm text-gray-500">Add items you love to your wishlist.</p>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                                {wishlistDetails.length > 0 && (
                                <div className="border-t border-gray-200 py-6 px-4 sm:px-6">
                                    <div className="flex justify-center text-center text-sm text-gray-500">
                                        <p>
                                            <button type="button" className="font-medium text-indigo-600 hover:text-indigo-500" onClick={onClose}>
                                                Continue Shopping
                                                <span aria-hidden="true"> &rarr;</span>
                                            </button>
                                        </p>
                                    </div>
                                </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Wishlist;
