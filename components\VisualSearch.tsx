
import { useState, useCallback, type FC, type DragEvent } from 'react';
import { GoogleGenAI } from "@google/genai";
import { products as allProducts } from '../data';
import type { Product } from '../types';
import Icon from '../Icon';
import ProductCard from '../ProductCard';

interface VisualSearchProps {
    isOpen: boolean;
    onClose: () => void;
    onProductSelect: (id: string) => void;
}

const getBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            const result = reader.result as string;
            // remove 'data:image/jpeg;base64,' part
            resolve(result.split(',')[1]);
        };
        reader.onerror = error => reject(error);
    });
};

const VisualSearch: FC<VisualSearchProps> = ({ isOpen, onClose, onProductSelect }) => {
    const [file, setFile] = useState<File | null>(null);
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [results, setResults] = useState<Product[]>([]);
    const [aiQuery, setAiQuery] = useState<string>('');
    const [isDragging, setIsDragging] = useState(false);

    const resetState = useCallback(() => {
        setFile(null);
        setPreviewUrl(null);
        setIsLoading(false);
        setError(null);
        setResults([]);
        setAiQuery('');
    }, []);

    const handleClose = () => {
        resetState();
        onClose();
    };

    const handleFileChange = (selectedFile: File | null) => {
        if (selectedFile && selectedFile.type.startsWith('image/')) {
            setFile(selectedFile);
            setPreviewUrl(URL.createObjectURL(selectedFile));
            setError(null);
            setResults([]);
            setAiQuery('');
        } else {
            setError("Please select a valid image file (JPEG, PNG, etc.).");
        }
    };

    const handleSearch = async () => {
        if (!file) return;

        setIsLoading(true);
        setError(null);
        setResults([]);
        
        try {
            const base64Image = await getBase64(file);
            const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

            const imagePart = {
                inlineData: {
                    mimeType: file.type,
                    data: base64Image,
                },
            };

            const textPart = {
                text: `You are a visual fashion analyst. Analyze the provided image. Identify the main clothing item a person might be interested in.
Respond ONLY with a single JSON object. The object should have a key "searchQuery" which is a string containing a descriptive search query for the main item. The query should include the item type, color, style, and any other relevant keywords.
For example, if the image shows a man wearing a blue denim jacket and black jeans, a good response would be: {"searchQuery": "blue denim jacket"}.
If it shows a person in a formal black suit, respond with: {"searchQuery": "black formal suit"}.
Focus on the most prominent and specific clothing item.`
            };

            const response = await ai.models.generateContent({
                model: 'gemini-2.5-flash-preview-04-17',
                contents: { parts: [imagePart, textPart] },
                config: {
                    responseMimeType: "application/json",
                }
            });

            let jsonStr = response.text.trim();
            const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
            const match = jsonStr.match(fenceRegex);
            if (match && match[2]) {
                jsonStr = match[2].trim();
            }

            const parsedData = JSON.parse(jsonStr);
            const query = parsedData.searchQuery;

            if (typeof query !== 'string' || !query) {
                throw new Error("AI did not return a valid search query.");
            }
            
            setAiQuery(query);

            const keywords = query.toLowerCase().split(' ').filter(k => k.length > 2);
            const searchResults = allProducts.filter(p => {
                const productText = `${p.name} ${p.description} ${p.category} ${p.tags.join(' ')}`.toLowerCase();
                const matchCount = keywords.reduce((acc, keyword) => {
                    return productText.includes(keyword) ? acc + 1 : acc;
                }, 0);
                return (matchCount / keywords.length) > 0.4; // Match >40% of keywords
            });

            setResults(searchResults);

        } catch (e) {
            console.error("Error during visual search:", e);
            setError("Sorry, we couldn't analyze that image. Please try another one.");
        } finally {
            setIsLoading(false);
        }
    };

    const handleDragEvents = (e: DragEvent<HTMLDivElement>, isEntering: boolean) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(isEntering);
    };

    const handleDrop = (e: DragEvent<HTMLDivElement>) => {
        handleDragEvents(e, false);
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFileChange(e.dataTransfer.files[0]);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="relative z-50" aria-labelledby="visual-search-title" role="dialog" aria-modal="true">
            <div className="fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity"></div>
            <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
                <div className="flex min-h-full items-start justify-center p-4 text-center sm:items-center sm:p-6">
                    <div className="relative transform overflow-hidden rounded-2xl bg-gray-50 text-left shadow-xl transition-all sm:my-8 w-full max-w-4xl">
                        <div className="flex justify-between items-center p-4 border-b border-gray-200 bg-white">
                            <h2 className="text-lg font-semibold text-gray-900" id="visual-search-title">Find Style with a Photo</h2>
                            <button onClick={handleClose} className="text-gray-400 hover:text-gray-600">
                                <Icon name="close" className="w-6 h-6"/>
                            </button>
                        </div>
                        <div className="p-6 min-h-[60vh]">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {/* Left: Uploader */}
                                <div>
                                    <div 
                                        className={`relative flex flex-col items-center justify-center w-full h-64 border-2 border-dashed rounded-lg cursor-pointer bg-white transition-colors ${isDragging ? 'border-indigo-500 bg-indigo-50' : 'border-gray-300 hover:border-gray-400'}`}
                                        onDragEnter={(e) => handleDragEvents(e, true)}
                                        onDragLeave={(e) => handleDragEvents(e, false)}
                                        onDragOver={(e) => e.preventDefault()}
                                        onDrop={handleDrop}
                                    >
                                        <input type="file" className="absolute inset-0 w-full h-full opacity-0 cursor-pointer" accept="image/*" onChange={(e) => handleFileChange(e.target.files ? e.target.files[0] : null)} />
                                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                            <Icon name="upload" className="w-10 h-10 mb-3 text-gray-400" />
                                            <p className="mb-2 text-sm text-gray-500"><span className="font-semibold">Click to upload</span> or drag and drop</p>
                                            <p className="text-xs text-gray-500">PNG, JPG, or WEBP</p>
                                        </div>
                                    </div>
                                    {previewUrl && (
                                        <div className="mt-4 p-4 bg-white rounded-lg border">
                                            <h3 className="font-semibold text-gray-800 mb-2">Your Image:</h3>
                                            <img src={previewUrl} alt="Preview" className="w-full h-auto max-h-48 object-contain rounded-md" />
                                            <div className="mt-4 flex gap-4">
                                                <button onClick={handleSearch} disabled={isLoading} className="flex-1 w-full flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-indigo-700 disabled:bg-gray-400">
                                                    {isLoading ? 'Analyzing...' : 'Find Matches'}
                                                </button>
                                                <button onClick={resetState} className="flex-1 w-full flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50">
                                                    Clear
                                                </button>
                                            </div>
                                        </div>
                                    )}
                                     {error && <p className="mt-4 text-sm text-red-600 bg-red-100 p-3 rounded-md">{error}</p>}
                                </div>
                                
                                {/* Right: Results */}
                                <div className="h-[60vh] overflow-y-auto pr-2 -mr-2">
                                    {isLoading ? (
                                        <div className="flex items-center justify-center h-full">
                                            <div className="flex items-center gap-3 text-gray-600">
                                                <div className="w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
                                                <span className="font-medium text-lg">Analyzing your style...</span>
                                            </div>
                                        </div>
                                    ) : results.length > 0 ? (
                                        <div>
                                            <h3 className="font-semibold text-gray-800 mb-4">Found {results.length} similar items for <span className="text-indigo-600">"{aiQuery}"</span></h3>
                                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                                {results.map(product => (
                                                    <ProductCard key={product.id} product={product} onSelect={onProductSelect} />
                                                ))}
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="flex items-center justify-center h-full text-center">
                                            <div>
                                                 <Icon name="camera" className="w-16 h-16 mx-auto text-gray-300"/>
                                                 <h3 className="mt-4 text-xl font-bold text-gray-900">Search by image</h3>
                                                 <p className="mt-2 text-md text-gray-500">Upload a photo to find similar styles in our catalog.</p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default VisualSearch;
