import { type FC } from 'react';
import Icon from './Icon';

interface SizeGuideProps {
  isOpen: boolean;
  onClose: () => void;
  category: 'Suits' | 'Casual' | 'Footwear' | 'Accessories';
}

const sizeGuides = {
  Suits: {
    title: 'Suit Size Guide',
    description: 'Find your perfect suit size with our comprehensive measurement guide.',
    measurements: [
      { size: 'S', chest: '36-38"', waist: '30-32"', length: '28"' },
      { size: 'M', chest: '38-40"', waist: '32-34"', length: '29"' },
      { size: 'L', chest: '40-42"', waist: '34-36"', length: '30"' },
      { size: 'XL', chest: '42-44"', waist: '36-38"', length: '31"' },
      { size: 'XXL', chest: '44-46"', waist: '38-40"', length: '32"' }
    ],
    tips: [
      'Measure your chest at the fullest part, keeping the tape measure level.',
      'For waist measurement, measure around your natural waistline.',
      'Length is measured from the shoulder to the desired hem length.',
      'Consider your preferred fit - slim, regular, or relaxed.'
    ]
  },
  Casual: {
    title: 'Casual Wear Size Guide',
    description: 'Get the perfect fit for shirts, t-shirts, and casual wear.',
    measurements: [
      { size: 'S', chest: '34-36"', length: '27"', sleeve: '32"' },
      { size: 'M', chest: '36-38"', length: '28"', sleeve: '33"' },
      { size: 'L', chest: '38-40"', length: '29"', sleeve: '34"' },
      { size: 'XL', chest: '40-42"', length: '30"', sleeve: '35"' },
      { size: 'XXL', chest: '42-44"', length: '31"', sleeve: '36"' }
    ],
    tips: [
      'Measure chest circumference at the widest point.',
      'Length is measured from the highest point of the shoulder.',
      'Sleeve length is from shoulder seam to wrist.',
      'Allow for comfortable movement and preferred fit style.'
    ]
  },
  Footwear: {
    title: 'Shoe Size Guide',
    description: 'Find your perfect shoe size with our conversion chart.',
    measurements: [
      { size: '7', us: '7', uk: '6', eu: '40', cm: '25.0' },
      { size: '8', us: '8', uk: '7', eu: '41', cm: '25.5' },
      { size: '9', us: '9', uk: '8', eu: '42', cm: '26.0' },
      { size: '10', us: '10', uk: '9', eu: '43', cm: '26.5' },
      { size: '11', us: '11', uk: '10', eu: '44', cm: '27.0' },
      { size: '12', us: '12', uk: '11', eu: '45', cm: '27.5' }
    ],
    tips: [
      'Measure your feet in the evening when they are at their largest.',
      'Stand on a piece of paper and trace your foot outline.',
      'Measure from heel to longest toe for accurate length.',
      'Consider the width of your foot and any specific fit preferences.'
    ]
  },
  Accessories: {
    title: 'Accessories Size Guide',
    description: 'Size guide for belts, ties, and other accessories.',
    measurements: [
      { item: 'Belt', size: 'S', measurement: '30-32"' },
      { item: 'Belt', size: 'M', measurement: '34-36"' },
      { item: 'Belt', size: 'L', measurement: '38-40"' },
      { item: 'Belt', size: 'XL', measurement: '42-44"' },
      { item: 'Tie', size: 'Standard', measurement: '57-59"' },
      { item: 'Bow Tie', size: 'Adjustable', measurement: '14-18"' }
    ],
    tips: [
      'For belts, measure your waist where you typically wear your belt.',
      'Standard tie length works for most men between 5\'8" and 6\'2".',
      'Bow ties are typically adjustable to fit neck sizes 14-18 inches.',
      'Consider the style and how the accessory will be worn.'
    ]
  }
};

const SizeGuide: FC<SizeGuideProps> = ({ isOpen, onClose, category }) => {
  if (!isOpen) return null;

  const guide = sizeGuides[category];

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      <div className="absolute inset-x-4 top-4 bottom-4 md:inset-x-auto md:left-1/2 md:transform md:-translate-x-1/2 md:w-full md:max-w-2xl bg-white rounded-lg shadow-2xl overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">{guide.title}</h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
          >
            <Icon name="x-mark" className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 overflow-auto max-h-[calc(100vh-200px)]">
          <p className="text-gray-600 mb-6">{guide.description}</p>

          {/* Size Chart */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Size Chart</h3>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    {category === 'Footwear' ? (
                      <>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-900">Size</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-900">US</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-900">UK</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-900">EU</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-900">CM</th>
                      </>
                    ) : category === 'Accessories' ? (
                      <>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-900">Item</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-900">Size</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-900">Measurement</th>
                      </>
                    ) : category === 'Casual' ? (
                      <>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-900">Size</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-900">Chest</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-900">Length</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-900">Sleeve</th>
                      </>
                    ) : (
                      <>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-900">Size</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-900">Chest</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-900">Waist</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-900">Length</th>
                      </>
                    )}
                  </tr>
                </thead>
                <tbody>
                  {guide.measurements.map((measurement, index) => (
                    <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      {category === 'Footwear' ? (
                        <>
                          <td className="border border-gray-300 px-4 py-2 font-medium">{measurement.size}</td>
                          <td className="border border-gray-300 px-4 py-2">{(measurement as any).us}</td>
                          <td className="border border-gray-300 px-4 py-2">{(measurement as any).uk}</td>
                          <td className="border border-gray-300 px-4 py-2">{(measurement as any).eu}</td>
                          <td className="border border-gray-300 px-4 py-2">{(measurement as any).cm}</td>
                        </>
                      ) : category === 'Accessories' ? (
                        <>
                          <td className="border border-gray-300 px-4 py-2 font-medium">{(measurement as any).item}</td>
                          <td className="border border-gray-300 px-4 py-2">{measurement.size}</td>
                          <td className="border border-gray-300 px-4 py-2">{(measurement as any).measurement}</td>
                        </>
                      ) : category === 'Casual' ? (
                        <>
                          <td className="border border-gray-300 px-4 py-2 font-medium">{measurement.size}</td>
                          <td className="border border-gray-300 px-4 py-2">{(measurement as any).chest}</td>
                          <td className="border border-gray-300 px-4 py-2">{(measurement as any).length}</td>
                          <td className="border border-gray-300 px-4 py-2">{(measurement as any).sleeve}</td>
                        </>
                      ) : (
                        <>
                          <td className="border border-gray-300 px-4 py-2 font-medium">{measurement.size}</td>
                          <td className="border border-gray-300 px-4 py-2">{(measurement as any).chest}</td>
                          <td className="border border-gray-300 px-4 py-2">{(measurement as any).waist}</td>
                          <td className="border border-gray-300 px-4 py-2">{(measurement as any).length}</td>
                        </>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Measurement Tips */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">How to Measure</h3>
            <ul className="space-y-2">
              {guide.tips.map((tip, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="flex-shrink-0 w-2 h-2 bg-indigo-600 rounded-full mt-2"></span>
                  <span className="text-gray-700">{tip}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Additional Help */}
          <div className="mt-8 p-4 bg-indigo-50 rounded-lg">
            <h4 className="font-semibold text-indigo-900 mb-2">Need Help?</h4>
            <p className="text-sm text-indigo-700">
              If you're between sizes or need additional assistance, please contact our customer service team. 
              We're here to help you find the perfect fit!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SizeGuide;
