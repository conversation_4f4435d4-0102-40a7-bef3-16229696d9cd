
import { useState, useCallback, type FC } from 'react';
import { GoogleGenAI } from "@google/genai";
import { reviews as allReviews } from '../data';
import Icon from './Icon';

interface ReviewSummaryProps {
    productId: string;
}

interface Summary {
    pros: string[];
    cons: string[];
}

const ReviewSummary: FC<ReviewSummaryProps> = ({ productId }) => {
    const [summary, setSummary] = useState<Summary | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const handleGenerateSummary = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        setSummary(null);

        try {
            const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });
            
            const productReviews = allReviews
                .filter(r => r.productId === productId)
                .map(r => r.comment);

            if (productReviews.length === 0) {
                throw new Error("No reviews available to summarize.");
            }
            
            const prompt = `You are a review analysis expert for Marcat, a men's clothing marketplace. I will provide you with a list of customer reviews for a specific product. Your task is to analyze these reviews and provide a concise summary of the main positive and negative points.

RULES:
1. Read all the provided reviews carefully.
2. Identify recurring themes for both praise (pros) and criticism (cons).
3. Summarize these themes into bullet points. Each bullet point should be a short, clear sentence.
4. Generate a maximum of 3 pros and 3 cons. If there are no clear points for one category, return an empty array for it.
5. Respond ONLY with a JSON object with the following structure: {"summary": {"pros": ["Fit is perfect", "Material feels high quality"], "cons": ["Runs slightly small", "Color is different than pictured"]}}

Here are the reviews:
${JSON.stringify(productReviews)}`;

            const response = await ai.models.generateContent({
                model: 'gemini-2.5-flash-preview-04-17',
                contents: prompt,
                config: {
                    responseMimeType: "application/json",
                }
            });
            
            let jsonStr = response.text.trim();
            const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
            const match = jsonStr.match(fenceRegex);
            if (match && match[2]) {
                jsonStr = match[2].trim();
            }

            const parsedData = JSON.parse(jsonStr);
            
            if (parsedData && parsedData.summary && Array.isArray(parsedData.summary.pros) && Array.isArray(parsedData.summary.cons)) {
                setSummary(parsedData.summary);
            } else {
                throw new Error("Invalid response format from AI.");
            }

        } catch (e) {
            console.error("Error generating review summary:", e);
            setError("Sorry, our AI couldn't summarize the reviews at this moment. Please try again.");
        } finally {
            setIsLoading(false);
        }
    }, [productId]);

    const renderContent = () => {
        if (isLoading) {
            return (
                <div className="flex items-center justify-center py-10">
                    <div className="flex items-center gap-3 text-gray-600">
                        <div className="w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
                        <span className="font-medium text-lg">Analyzing reviews...</span>
                    </div>
                </div>
            );
        }

        if (error) {
            return (
                <div className="text-center py-10 px-4 bg-red-50 rounded-lg">
                    <p className="text-lg font-medium text-red-700">{error}</p>
                     <button
                        onClick={handleGenerateSummary}
                        className="mt-4 inline-flex items-center gap-2 rounded-md border border-transparent bg-red-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-red-700"
                    >
                        Try Again
                    </button>
                </div>
            );
        }

        if (summary) {
            const hasPros = summary.pros.length > 0;
            const hasCons = summary.cons.length > 0;

            if (!hasPros && !hasCons) {
                 return (
                    <div className="text-center py-10 px-4">
                        <p className="text-lg font-medium text-gray-700">Our AI analyzed the reviews but couldn't find strong recurring themes. It seems opinions are quite varied!</p>
                    </div>
                );
            }

            return (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                    {hasPros && (
                        <div>
                            <h4 className="flex items-center gap-2 font-semibold text-gray-800">
                                <Icon name="check-circle" className="w-6 h-6 text-green-500" />
                                What buyers loved
                            </h4>
                            <ul className="mt-3 space-y-2 pl-1">
                                {summary.pros.map((pro, i) => (
                                    <li key={`pro-${i}`} className="flex items-start gap-2 text-gray-600">
                                        <span className="mt-1 flex-shrink-0 w-1.5 h-1.5 bg-gray-400 rounded-full"></span>
                                        {pro}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    )}
                    {hasCons && (
                         <div>
                            <h4 className="flex items-center gap-2 font-semibold text-gray-800">
                                <Icon name="exclamation-circle" className="w-6 h-6 text-yellow-500" />
                                Things to consider
                            </h4>
                            <ul className="mt-3 space-y-2 pl-1">
                                {summary.cons.map((con, i) => (
                                    <li key={`con-${i}`} className="flex items-start gap-2 text-gray-600">
                                        <span className="mt-1 flex-shrink-0 w-1.5 h-1.5 bg-gray-400 rounded-full"></span>
                                        {con}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    )}
                </div>
            );
        }

        return (
            <div className="text-center">
                <button
                    onClick={handleGenerateSummary}
                    className="inline-flex items-center justify-center gap-2 rounded-full border border-transparent bg-indigo-600 px-8 py-3 text-base font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                >
                     <Icon name="sparkles" className="w-5 h-5"/>
                    Summarize with AI
                </button>
            </div>
        );
    };

    return (
        <div className="bg-indigo-50/70 rounded-xl p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-1 text-center">Tired of reading?</h3>
            <p className="text-center text-gray-600 mb-6">Let our AI summarize the reviews for you.</p>
            {renderContent()}
        </div>
    );
};

export default ReviewSummary;
