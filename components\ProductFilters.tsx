import { useState, useMemo, type FC } from 'react';
import { stores, products as allProducts, reviews as allReviews } from '../data';
import type { Product } from '../types';
import Icon from './Icon';
import StarRating from './StarRating';

interface FilterOptions {
  priceRange: [number, number];
  selectedSizes: string[];
  selectedColors: string[];
  selectedStores: string[];
  minRating: number;
  sortBy: 'name' | 'price-low' | 'price-high' | 'rating' | 'newest';
}

interface ProductFiltersProps {
  products: Product[];
  onFilteredProductsChange: (products: Product[]) => void;
  isOpen: boolean;
  onToggle: () => void;
}

const getDiscountedPrice = (price: number, discountPercentage?: number): number => {
  if (!discountPercentage) return price;
  return price * (1 - discountPercentage / 100);
};

const ProductFilters: FC<ProductFiltersProps> = ({ 
  products, 
  onFilteredProductsChange, 
  isOpen, 
  onToggle 
}) => {
  const [filters, setFilters] = useState<FilterOptions>({
    priceRange: [0, 1000],
    selectedSizes: [],
    selectedColors: [],
    selectedStores: [],
    minRating: 0,
    sortBy: 'newest'
  });

  // Extract available options from products
  const filterOptions = useMemo(() => {
    const sizes = new Set<string>();
    const colors = new Set<string>();
    const storeIds = new Set<string>();
    let minPrice = Infinity;
    let maxPrice = 0;

    products.forEach(product => {
      const discountedPrice = getDiscountedPrice(product.basePrice, product.offer?.discountPercentage);
      minPrice = Math.min(minPrice, discountedPrice);
      maxPrice = Math.max(maxPrice, discountedPrice);
      
      storeIds.add(product.storeId);
      
      product.variants.forEach(variant => {
        sizes.add(variant.size);
        colors.add(variant.color);
      });
    });

    return {
      sizes: Array.from(sizes).sort(),
      colors: Array.from(colors),
      stores: stores.filter(store => storeIds.has(store.id)),
      priceRange: [Math.floor(minPrice), Math.ceil(maxPrice)] as [number, number]
    };
  }, [products]);

  // Apply filters and sorting
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = products.filter(product => {
      const discountedPrice = getDiscountedPrice(product.basePrice, product.offer?.discountPercentage);
      
      // Price filter
      if (discountedPrice < filters.priceRange[0] || discountedPrice > filters.priceRange[1]) {
        return false;
      }
      
      // Size filter
      if (filters.selectedSizes.length > 0) {
        const productSizes = product.variants.map(v => v.size);
        if (!filters.selectedSizes.some(size => productSizes.includes(size))) {
          return false;
        }
      }
      
      // Color filter
      if (filters.selectedColors.length > 0) {
        const productColors = product.variants.map(v => v.color);
        if (!filters.selectedColors.some(color => productColors.includes(color))) {
          return false;
        }
      }
      
      // Store filter
      if (filters.selectedStores.length > 0) {
        if (!filters.selectedStores.includes(product.storeId)) {
          return false;
        }
      }
      
      // Rating filter
      if (filters.minRating > 0) {
        const productReviews = allReviews.filter(r => r.productId === product.id);
        if (productReviews.length === 0) return false;
        
        const avgRating = productReviews.reduce((acc, review) => acc + review.rating, 0) / productReviews.length;
        if (avgRating < filters.minRating) {
          return false;
        }
      }
      
      return true;
    });

    // Apply sorting
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'price-low':
          return getDiscountedPrice(a.basePrice, a.offer?.discountPercentage) - 
                 getDiscountedPrice(b.basePrice, b.offer?.discountPercentage);
        case 'price-high':
          return getDiscountedPrice(b.basePrice, b.offer?.discountPercentage) - 
                 getDiscountedPrice(a.basePrice, a.offer?.discountPercentage);
        case 'rating':
          const aReviews = allReviews.filter(r => r.productId === a.id);
          const bReviews = allReviews.filter(r => r.productId === b.id);
          const aRating = aReviews.length > 0 ? aReviews.reduce((acc, r) => acc + r.rating, 0) / aReviews.length : 0;
          const bRating = bReviews.length > 0 ? bReviews.reduce((acc, r) => acc + r.rating, 0) / bReviews.length : 0;
          return bRating - aRating;
        case 'newest':
        default:
          return 0; // Keep original order for newest
      }
    });

    return filtered;
  }, [products, filters]);

  // Update parent component when filtered products change
  useMemo(() => {
    onFilteredProductsChange(filteredAndSortedProducts);
  }, [filteredAndSortedProducts, onFilteredProductsChange]);

  const updateFilter = <K extends keyof FilterOptions>(key: K, value: FilterOptions[K]) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const toggleArrayFilter = <T,>(key: keyof FilterOptions, value: T) => {
    setFilters(prev => {
      const currentArray = prev[key] as T[];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];
      return { ...prev, [key]: newArray };
    });
  };

  const clearAllFilters = () => {
    setFilters({
      priceRange: filterOptions.priceRange,
      selectedSizes: [],
      selectedColors: [],
      selectedStores: [],
      minRating: 0,
      sortBy: 'newest'
    });
  };

  const activeFiltersCount = 
    (filters.selectedSizes.length > 0 ? 1 : 0) +
    (filters.selectedColors.length > 0 ? 1 : 0) +
    (filters.selectedStores.length > 0 ? 1 : 0) +
    (filters.minRating > 0 ? 1 : 0) +
    (filters.priceRange[0] !== filterOptions.priceRange[0] || filters.priceRange[1] !== filterOptions.priceRange[1] ? 1 : 0);

  return (
    <div className="relative">
      {/* Filter Toggle Button */}
      <button
        onClick={onToggle}
        className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
      >
        <Icon name="funnel" className="w-5 h-5" />
        <span>Filters</span>
        {activeFiltersCount > 0 && (
          <span className="bg-indigo-600 text-white text-xs px-2 py-1 rounded-full">
            {activeFiltersCount}
          </span>
        )}
      </button>

      {/* Filter Panel */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
            <div className="flex items-center gap-2">
              {activeFiltersCount > 0 && (
                <button
                  onClick={clearAllFilters}
                  className="text-sm text-indigo-600 hover:text-indigo-700"
                >
                  Clear All
                </button>
              )}
              <button
                onClick={onToggle}
                className="text-gray-400 hover:text-gray-600"
              >
                <Icon name="x-mark" className="w-5 h-5" />
              </button>
            </div>
          </div>

          <div className="space-y-6">
            {/* Sort By */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
              <select
                value={filters.sortBy}
                onChange={(e) => updateFilter('sortBy', e.target.value as FilterOptions['sortBy'])}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="newest">Newest</option>
                <option value="name">Name A-Z</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="rating">Highest Rated</option>
              </select>
            </div>

            {/* Price Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price Range: ${filters.priceRange[0]} - ${filters.priceRange[1]}
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="range"
                  min={filterOptions.priceRange[0]}
                  max={filterOptions.priceRange[1]}
                  value={filters.priceRange[0]}
                  onChange={(e) => updateFilter('priceRange', [Number(e.target.value), filters.priceRange[1]])}
                  className="flex-1"
                />
                <input
                  type="range"
                  min={filterOptions.priceRange[0]}
                  max={filterOptions.priceRange[1]}
                  value={filters.priceRange[1]}
                  onChange={(e) => updateFilter('priceRange', [filters.priceRange[0], Number(e.target.value)])}
                  className="flex-1"
                />
              </div>
            </div>

            {/* Minimum Rating */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Minimum Rating</label>
              <div className="flex items-center gap-2">
                {[1, 2, 3, 4, 5].map(rating => (
                  <button
                    key={rating}
                    onClick={() => updateFilter('minRating', filters.minRating === rating ? 0 : rating)}
                    className={`flex items-center gap-1 px-2 py-1 rounded text-sm transition-colors ${
                      filters.minRating === rating
                        ? 'bg-indigo-100 text-indigo-700'
                        : 'hover:bg-gray-100'
                    }`}
                  >
                    <StarRating rating={rating} size="sm" />
                    <span>{rating}+</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Sizes */}
            {filterOptions.sizes.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Sizes</label>
                <div className="flex flex-wrap gap-2">
                  {filterOptions.sizes.map(size => (
                    <button
                      key={size}
                      onClick={() => toggleArrayFilter('selectedSizes', size)}
                      className={`px-3 py-1 rounded text-sm transition-colors ${
                        filters.selectedSizes.includes(size)
                          ? 'bg-indigo-600 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Colors */}
            {filterOptions.colors.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Colors</label>
                <div className="flex flex-wrap gap-2">
                  {filterOptions.colors.map(color => (
                    <button
                      key={color}
                      onClick={() => toggleArrayFilter('selectedColors', color)}
                      className={`px-3 py-1 rounded text-sm transition-colors ${
                        filters.selectedColors.includes(color)
                          ? 'bg-indigo-600 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {color}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Stores */}
            {filterOptions.stores.length > 1 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Stores</label>
                <div className="space-y-2">
                  {filterOptions.stores.map(store => (
                    <label key={store.id} className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={filters.selectedStores.includes(store.id)}
                        onChange={() => toggleArrayFilter('selectedStores', store.id)}
                        className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                      />
                      <span className="text-sm text-gray-700">{store.name}</span>
                    </label>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductFilters;
