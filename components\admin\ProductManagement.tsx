
import { useMemo, type FC } from 'react';
import { products as allProducts } from '../../data';

const ProductManagement: FC = () => {
    const productsWithStock = useMemo(() => {
        return allProducts.map(p => ({
            ...p,
            totalStock: p.variants.reduce((acc, v) => acc + v.stock, 0),
        }));
    }, []);

    return (
        <div>
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">Product Management</h1>
            <div className="bg-white shadow-md rounded-lg overflow-x-auto">
                <table className="min-w-full leading-normal">
                    <thead>
                        <tr className="bg-gray-100 text-left text-gray-600 uppercase text-sm leading-normal">
                            <th className="py-3 px-4 md:px-6">Image</th>
                            <th className="py-3 px-4 md:px-6">Product</th>
                            <th className="py-3 px-4 md:px-6">Category</th>
                            <th className="py-3 px-4 md:px-6 text-center">Price</th>
                            <th className="py-3 px-4 md:px-6 text-center">Stock</th>
                            <th className="py-3 px-4 md:px-6 text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody className="text-gray-800 text-sm font-light">
                        {productsWithStock.map(product => (
                            <tr key={product.id} className="border-b border-gray-200 hover:bg-gray-50">
                                <td className="py-3 px-4 md:px-6 text-left whitespace-nowrap">
                                    <img src={product.variants[0].imageUrl} alt={product.name} className="w-12 h-12 rounded-md object-cover" />
                                </td>
                                <td className="py-3 px-4 md:px-6 text-left">
                                    <span className="font-medium">{product.name}</span>
                                </td>
                                <td className="py-3 px-4 md:px-6 text-left">
                                    <span className="bg-gray-200 text-gray-600 py-1 px-3 rounded-full text-xs">{product.category}</span>
                                </td>
                                <td className="py-3 px-4 md:px-6 text-center font-semibold">
                                    ${product.basePrice.toFixed(2)}
                                </td>
                                <td className="py-3 px-4 md:px-6 text-center">
                                    <span className={`font-medium ${product.totalStock < 10 ? 'text-red-500' : 'text-gray-700'}`}>
                                        {product.totalStock} units
                                    </span>
                                </td>
                                <td className="py-3 px-4 md:px-6 text-center">
                                    <div className="flex item-center justify-center gap-2">
                                        <button className="text-xs sm:text-sm bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded-full transition-colors">Edit</button>
                                        <button className="text-xs sm:text-sm bg-red-500 hover:bg-red-600 text-white py-1 px-3 rounded-full transition-colors">Delete</button>
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default ProductManagement;
