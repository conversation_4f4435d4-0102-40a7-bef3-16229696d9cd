import { useState, useMemo, type FC } from 'react';
import type { Order } from '../types';
import Icon from './Icon';

interface OrderTrackingProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
}

// Mock orders data - in a real app, this would come from an API
const mockOrders: Order[] = [
  {
    id: 'ORD-001',
    userId: 'user-1',
    items: [
      { productId: 'prod-1', variantId: 'var-1a', quantity: 1, unitPrice: 424.99 }
    ],
    totalAmount: 424.99,
    status: 'shipped',
    shippingAddress: {
      id: 'addr-1',
      name: '<PERSON>',
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'USA',
      phone: '******-0123'
    },
    billingAddress: {
      id: 'addr-1',
      name: '<PERSON>',
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'USA'
    },
    paymentMethod: {
      id: 'pm-1',
      type: 'credit_card',
      last4: '4242',
      brand: 'Visa',
      expiryMonth: 12,
      expiryYear: 2025
    },
    orderDate: '2024-01-15T10:30:00Z',
    estimatedDelivery: '2024-01-20T18:00:00Z',
    trackingNumber: 'TRK123456789'
  },
  {
    id: 'ORD-002',
    userId: 'user-1',
    items: [
      { productId: 'prod-2', variantId: 'var-2a', quantity: 2, unitPrice: 79.99 }
    ],
    totalAmount: 159.98,
    status: 'processing',
    shippingAddress: {
      id: 'addr-1',
      name: 'John Doe',
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'USA',
      phone: '******-0123'
    },
    billingAddress: {
      id: 'addr-1',
      name: 'John Doe',
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'USA'
    },
    paymentMethod: {
      id: 'pm-1',
      type: 'credit_card',
      last4: '4242',
      brand: 'Visa'
    },
    orderDate: '2024-01-18T14:20:00Z',
    estimatedDelivery: '2024-01-25T18:00:00Z'
  }
];

const getStatusColor = (status: Order['status']) => {
  switch (status) {
    case 'pending': return 'bg-yellow-100 text-yellow-800';
    case 'confirmed': return 'bg-blue-100 text-blue-800';
    case 'processing': return 'bg-purple-100 text-purple-800';
    case 'shipped': return 'bg-indigo-100 text-indigo-800';
    case 'delivered': return 'bg-green-100 text-green-800';
    case 'cancelled': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getStatusIcon = (status: Order['status']) => {
  switch (status) {
    case 'pending': return 'clock';
    case 'confirmed': return 'check-circle';
    case 'processing': return 'cog';
    case 'shipped': return 'truck';
    case 'delivered': return 'check-circle';
    case 'cancelled': return 'x-circle';
    default: return 'question-mark-circle';
  }
};

const OrderCard: FC<{ order: Order }> = ({ order }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
      <div className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Order {order.id}</h3>
            <p className="text-sm text-gray-500">
              Placed on {new Date(order.orderDate).toLocaleDateString()}
            </p>
          </div>
          <div className="text-right">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
              {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
            </span>
            <p className="text-lg font-semibold text-gray-900 mt-1">
              ${order.totalAmount.toFixed(2)}
            </p>
          </div>
        </div>

        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-600">
              {order.items.length} item{order.items.length !== 1 ? 's' : ''}
            </span>
            {order.trackingNumber && (
              <span className="text-sm text-gray-600">
                Tracking: {order.trackingNumber}
              </span>
            )}
          </div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-indigo-600 hover:text-indigo-700 text-sm font-medium"
          >
            {isExpanded ? 'Hide Details' : 'View Details'}
          </button>
        </div>

        {isExpanded && (
          <div className="mt-6 border-t border-gray-200 pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Shipping Address</h4>
                <div className="text-sm text-gray-600">
                  <p>{order.shippingAddress.name}</p>
                  <p>{order.shippingAddress.street}</p>
                  <p>{order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}</p>
                  <p>{order.shippingAddress.country}</p>
                  {order.shippingAddress.phone && <p>{order.shippingAddress.phone}</p>}
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Payment Method</h4>
                <div className="text-sm text-gray-600">
                  <p>{order.paymentMethod.brand} ending in {order.paymentMethod.last4}</p>
                </div>
                {order.estimatedDelivery && (
                  <div className="mt-4">
                    <h4 className="font-medium text-gray-900 mb-2">Estimated Delivery</h4>
                    <p className="text-sm text-gray-600">
                      {new Date(order.estimatedDelivery).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const OrderTracking: FC<OrderTrackingProps> = ({ isOpen, onClose, userId }) => {
  const userOrders = useMemo(() => {
    return mockOrders.filter(order => order.userId === userId)
      .sort((a, b) => new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime());
  }, [userId]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      <div className="absolute inset-x-4 top-4 bottom-4 md:inset-x-auto md:left-1/2 md:transform md:-translate-x-1/2 md:w-full md:max-w-4xl bg-white rounded-lg shadow-2xl overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">Order History</h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
          >
            <Icon name="x-mark" className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 overflow-auto max-h-[calc(100vh-200px)]">
          {userOrders.length === 0 ? (
            <div className="text-center py-20">
              <Icon name="shopping-bag" className="w-16 h-16 mx-auto text-gray-300" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">No orders yet</h3>
              <p className="mt-2 text-sm text-gray-500">
                When you place your first order, it will appear here.
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {userOrders.map((order) => (
                <OrderCard key={order.id} order={order} />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderTracking;
