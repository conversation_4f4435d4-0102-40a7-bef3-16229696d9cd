export interface User {
  id: string;
  name: string;
  email: string;
  role: 'customer' | 'seller' | 'admin';
}

export interface Store {
  id: string;
  name: string;
  logoUrl: string;
  rating: number;
}

export interface ProductVariant {
  id: string;
  color: string;
  colorHex: string;
  size: 'S' | 'M' | 'L' | 'XL' | 'XXL';
  stock: number;
  imageUrl: string;
}

export interface Offer {
  discountPercentage: number;
  validUntil: string; // ISO date string
}

export interface Product {
  id:string;
  name: string;
  description: string;
  category: 'Suits' | 'Casual' | 'Footwear' | 'Accessories';
  storeId: string;
  basePrice: number;
  variants: ProductVariant[];
  offer?: Offer;
  tags: string[];
}

export interface CartItem {
  productId: string;
  variantId: string;
  quantity: number;
  unitPrice: number;
}

export interface Review {
  id: string;
  productId: string;
  userId: string;
  rating: number; // 1-5
  comment: string;
  createdAt: string; // ISO date string
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'model';
  text: string;
}

export interface ComparisonItem {
  productId: string;
  addedAt: string; // ISO date string
}

export interface Order {
  id: string;
  userId: string;
  items: CartItem[];
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  shippingAddress: Address;
  billingAddress: Address;
  paymentMethod: PaymentMethod;
  orderDate: string; // ISO date string
  estimatedDelivery?: string; // ISO date string
  trackingNumber?: string;
  promoCode?: string;
  discount?: number;
}

export interface Address {
  id: string;
  name: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone?: string;
}

export interface PaymentMethod {
  id: string;
  type: 'credit_card' | 'debit_card' | 'paypal' | 'apple_pay';
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
}

export interface PromoCode {
  code: string;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  minOrderAmount?: number;
  maxDiscount?: number;
  validFrom: string; // ISO date string
  validUntil: string; // ISO date string
  usageLimit?: number;
  usedCount: number;
  isActive: boolean;
}

export interface LoyaltyProgram {
  userId: string;
  points: number;
  tier: 'bronze' | 'silver' | 'gold' | 'platinum';
  totalSpent: number;
  joinDate: string; // ISO date string
}
