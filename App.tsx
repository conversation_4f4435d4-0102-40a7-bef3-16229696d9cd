
import { useState, useMemo, useCallback, Suspense, type FC } from 'react';
import { useAuth } from './context/AuthContext';
import { CartProvider } from './context/CartContext';
import { WishlistProvider } from './context/WishlistContext';
import { ComparisonProvider, useComparison } from './context/ComparisonContext';
import { stores, products as allProducts } from './data';
import type { Product } from './types';
import Header from './Header';
import ProductList from './components/ProductList';
import Cart from './components/Cart';
import AuthModal from './components/AuthModal';
import Icon from './Icon';
import Wishlist from './components/Wishlist';
import VisualSearch from './components/VisualSearch';
import {
  LazyProductDetail,
  LazyCheckout,
  LazyPersonalShopper,
  LazyProductComparison,
  LazyOrderTracking,
  LazyLoyaltyProgram,
  LazyStyleRecommendations,
  LazyAdminDashboard,
  ComponentLoader,
  LazyErrorBoundary
} from './components/LazyComponents';
import { performanceMonitor, useRenderTime } from './utils/performance';
import { cache, CACHE_KEYS, CACHE_TTL } from './utils/cache';

const AppContent: FC = () => {
    const { currentUser } = useAuth();
    const { comparisonCount } = useComparison();

    // Performance monitoring
    useRenderTime('AppContent');
    const [view, setView] = useState<'list' | 'detail' | 'checkout' | 'admin'>('list');
    const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
    const [isCartOpen, setCartOpen] = useState(false);
    const [isWishlistOpen, setWishlistOpen] = useState(false);
    const [isAuthModalOpen, setAuthModalOpen] = useState(false);
    const [isShopperOpen, setShopperOpen] = useState(false);
    const [isVisualSearchOpen, setVisualSearchOpen] = useState(false);
    const [isComparisonOpen, setComparisonOpen] = useState(false);
    const [isOrderTrackingOpen, setOrderTrackingOpen] = useState(false);
    const [isLoyaltyOpen, setLoyaltyOpen] = useState(false);
    const [isStyleRecommendationsOpen, setStyleRecommendationsOpen] = useState(false);
    const [activeCategory, setActiveCategory] = useState<string>('All');
    const [searchQuery, setSearchQuery] = useState('');

    const handleProductSelect = useCallback((id: string) => {
        setSelectedProductId(id);
        setView('detail');
        window.scrollTo(0, 0);
        setWishlistOpen(false); // Close wishlist if a product is selected from it
    }, []);
    
    const handleVisualSearchSelect = useCallback((id: string) => {
        setVisualSearchOpen(false);
        // Delay to allow modal to close before navigating
        setTimeout(() => handleProductSelect(id), 300);
    }, [handleProductSelect]);

    const handleBackToList = useCallback(() => {
        setSelectedProductId(null);
        setView('list');
        setSearchQuery('');
    }, []);
    
    const handleShopperProductSelect = useCallback((id: string) => {
        setShopperOpen(false);
        // Delay navigation slightly to allow the modal to close gracefully
        setTimeout(() => handleProductSelect(id), 100);
    }, [handleProductSelect]);

    const handleNavigateToCheckout = useCallback(() => {
        setCartOpen(false);
        setView('checkout');
        window.scrollTo(0, 0);
    }, []);

    const handleOrderPlaced = useCallback(() => {
        alert('Thank you for your order! It has been placed successfully.');
        setView('list');
        window.scrollTo(0, 0);
    }, []);

    const handleNavigateToAdmin = useCallback(() => {
        setView('admin');
        window.scrollTo(0, 0);
    }, []);

    const selectedProduct = useMemo(() => {
        if (!selectedProductId) return null;
        return allProducts.find(p => p.id === selectedProductId) || null;
    }, [selectedProductId]);
    
    const filteredProducts = useMemo(() => {
        let products = allProducts;
        
        if (activeCategory === 'Offers') {
            products = products.filter(p => !!p.offer);
        } else if (activeCategory !== 'All') {
            products = products.filter(p => p.category === activeCategory);
        }

        if (searchQuery.trim() !== '') {
            const lowercasedQuery = searchQuery.toLowerCase();
            const searchKeywords = lowercasedQuery.split(' ').filter(k => k);

            products = products.filter(p => {
                const store = stores.find(s => s.id === p.storeId);
                const productText = `
                    ${p.name.toLowerCase()} 
                    ${p.description.toLowerCase()} 
                    ${p.tags.join(' ').toLowerCase()} 
                    ${store ? store.name.toLowerCase() : ''}
                `;
                return searchKeywords.every(keyword => productText.includes(keyword));
            });
        }
        
        return products;
    }, [activeCategory, searchQuery]);

    const categories = useMemo(() => ['All', 'Offers', ...Array.from(new Set(allProducts.map(p => p.category)))], []);
    
    const handleCategoryChange = useCallback((category: string) => {
        setActiveCategory(category);
        setSearchQuery('');
        setView('list');
    }, []);

    const renderView = () => {
        switch (view) {
            case 'list':
                return (
                    <ProductList 
                        products={filteredProducts} 
                        onProductSelect={handleProductSelect}
                        searchQuery={searchQuery}
                    />
                );
            case 'detail':
                return selectedProduct && (
                    <LazyErrorBoundary>
                        <Suspense fallback={<ComponentLoader />}>
                            <LazyProductDetail
                                product={selectedProduct}
                                onBack={handleBackToList}
                                onCartAdd={() => setCartOpen(true)}
                                onProductSelect={handleProductSelect}
                            />
                        </Suspense>
                    </LazyErrorBoundary>
                );
            case 'admin':
                if (currentUser?.role !== 'admin') {
                    return (
                        <div className="text-center py-20 px-6 bg-white rounded-lg shadow-xl">
                            <h1 className="text-3xl font-bold text-gray-800">Access Denied</h1>
                            <p className="mt-2 text-lg text-gray-600">You do not have permission to view this page.</p>
                            <button 
                                onClick={handleBackToList}
                                className="mt-6 inline-flex items-center justify-center gap-2 rounded-md border border-transparent bg-indigo-600 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-indigo-700"
                            >
                                Return to Homepage
                            </button>
                        </div>
                    )
                }
                return (
                    <LazyErrorBoundary>
                        <Suspense fallback={<ComponentLoader />}>
                            <LazyAdminDashboard />
                        </Suspense>
                    </LazyErrorBoundary>
                );
            case 'checkout':
                return (
                    <LazyErrorBoundary>
                        <Suspense fallback={<ComponentLoader />}>
                            <LazyCheckout
                                onBackToList={handleBackToList}
                                onOrderPlaced={handleOrderPlaced}
                            />
                        </Suspense>
                    </LazyErrorBoundary>
                );
            default:
                return null;
        }
    }

    return (
        <div className="bg-gray-50 min-h-screen text-gray-800">
                    <Header 
                        onCartClick={() => setCartOpen(true)}
                        onWishlistClick={() => setWishlistOpen(true)}
                        onUserClick={() => setAuthModalOpen(true)}
                        onVisualSearchClick={() => setVisualSearchOpen(true)}
                        onComparisonClick={() => setComparisonOpen(true)}
                        onOrderTrackingClick={() => setOrderTrackingOpen(true)}
                        onLoyaltyClick={() => setLoyaltyOpen(true)}
                        onAdminClick={handleNavigateToAdmin}
                        categories={categories}
                        activeCategory={activeCategory}
                        onCategoryChange={handleCategoryChange}
                        onLogoClick={handleBackToList}
                        searchQuery={searchQuery}
                        onSearchChange={setSearchQuery}
                    />
                    <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
                        {renderView()}
                    </main>
                    <Cart 
                        isOpen={isCartOpen} 
                        onClose={() => setCartOpen(false)}
                        onCheckoutClick={handleNavigateToCheckout}
                    />
                    <Wishlist
                        isOpen={isWishlistOpen}
                        onClose={() => setWishlistOpen(false)}
                        onProductSelect={handleProductSelect}
                    />
                    <AuthModal isOpen={isAuthModalOpen} onClose={() => setAuthModalOpen(false)} />
                    <LazyErrorBoundary>
                        <Suspense fallback={<ComponentLoader />}>
                            <LazyPersonalShopper
                                isOpen={isShopperOpen}
                                onClose={() => setShopperOpen(false)}
                                onProductSelect={handleShopperProductSelect}
                            />
                        </Suspense>
                    </LazyErrorBoundary>
                    <VisualSearch
                        isOpen={isVisualSearchOpen}
                        onClose={() => setVisualSearchOpen(false)}
                        onProductSelect={handleVisualSearchSelect}
                    />
                    <LazyErrorBoundary>
                        <Suspense fallback={<ComponentLoader />}>
                            <LazyProductComparison
                                isOpen={isComparisonOpen}
                                onClose={() => setComparisonOpen(false)}
                                onProductSelect={handleProductSelect}
                            />
                        </Suspense>
                    </LazyErrorBoundary>
                    <LazyErrorBoundary>
                        <Suspense fallback={<ComponentLoader />}>
                            <LazyOrderTracking
                                isOpen={isOrderTrackingOpen}
                                onClose={() => setOrderTrackingOpen(false)}
                                userId={currentUser?.id || ''}
                            />
                        </Suspense>
                    </LazyErrorBoundary>
                    <LazyErrorBoundary>
                        <Suspense fallback={<ComponentLoader />}>
                            <LazyLoyaltyProgram
                                isOpen={isLoyaltyOpen}
                                onClose={() => setLoyaltyOpen(false)}
                                userId={currentUser?.id || ''}
                            />
                        </Suspense>
                    </LazyErrorBoundary>
                    <LazyErrorBoundary>
                        <Suspense fallback={<ComponentLoader />}>
                            <LazyStyleRecommendations
                                isOpen={isStyleRecommendationsOpen}
                                onClose={() => setStyleRecommendationsOpen(false)}
                                onProductSelect={handleProductSelect}
                            />
                        </Suspense>
                    </LazyErrorBoundary>

            {/* Floating Action Buttons */}
            <div className="fixed bottom-6 right-6 z-40 flex flex-col gap-3">
                {/* Comparison FAB - only show when items are in comparison */}
                {comparisonCount > 0 && (
                    <button
                        onClick={() => setComparisonOpen(true)}
                        className="flex items-center justify-center w-14 h-14 bg-indigo-600 text-white rounded-full shadow-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-transform hover:scale-110 relative"
                        aria-label="Open Product Comparison"
                    >
                        <Icon name="scale" className="w-6 h-6"/>
                        <span className="absolute -top-2 -right-2 flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-red-600 rounded-full">
                            {comparisonCount}
                        </span>
                    </button>
                )}

                {/* Style Recommendations FAB */}
                <button
                    onClick={() => setStyleRecommendationsOpen(true)}
                    className="flex items-center justify-center w-14 h-14 bg-purple-600 text-white rounded-full shadow-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-transform hover:scale-110"
                    aria-label="Open Style Recommendations"
                >
                    <Icon name="sparkles" className="w-6 h-6"/>
                </button>

                {/* AI Personal Shopper FAB */}
                <button
                    onClick={() => setShopperOpen(true)}
                    className="flex items-center justify-center w-16 h-16 bg-indigo-600 text-white rounded-full shadow-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-transform hover:scale-110"
                    aria-label="Open AI Personal Shopper"
                >
                    <Icon name="chat-bubble-left-ellipsis" className="w-8 h-8"/>
                </button>
            </div>
        </div>
    );
};

const App: FC = () => {
    return (
        <ComparisonProvider>
            <WishlistProvider>
                <CartProvider>
                    <AppContent />
                </CartProvider>
            </WishlistProvider>
        </ComparisonProvider>
    );
};

export default App;
