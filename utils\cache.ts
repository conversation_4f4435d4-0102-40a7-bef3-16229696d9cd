// Simple in-memory cache with TTL (Time To Live) support
interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

class Cache {
  private cache = new Map<string, CacheItem<any>>();
  private readonly defaultTTL = 5 * 60 * 1000; // 5 minutes

  set<T>(key: string, data: T, ttl?: number): void {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    };
    this.cache.set(key, item);
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // Check if item has expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  has(key: string): boolean {
    const item = this.cache.get(key);
    
    if (!item) {
      return false;
    }

    // Check if item has expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  // Get cache statistics
  getStats() {
    const now = Date.now();
    let validItems = 0;
    let expiredItems = 0;

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        expiredItems++;
      } else {
        validItems++;
      }
    }

    return {
      totalItems: this.cache.size,
      validItems,
      expiredItems,
      hitRate: validItems / (validItems + expiredItems) || 0
    };
  }

  // Clean up expired items
  cleanup(): number {
    const now = Date.now();
    let removedCount = 0;

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
        removedCount++;
      }
    }

    return removedCount;
  }
}

// Create singleton cache instance
export const cache = new Cache();

// Cache keys constants
export const CACHE_KEYS = {
  PRODUCTS: 'products',
  PRODUCT_DETAIL: (id: string) => `product_detail_${id}`,
  REVIEWS: (productId: string) => `reviews_${productId}`,
  USER_PROFILE: (userId: string) => `user_profile_${userId}`,
  SEARCH_RESULTS: (query: string) => `search_${query}`,
  RECOMMENDATIONS: (userId: string) => `recommendations_${userId}`,
  ANALYTICS: 'analytics_data',
  ORDERS: (userId: string) => `orders_${userId}`,
  WISHLIST: (userId: string) => `wishlist_${userId}`,
  CART: (userId: string) => `cart_${userId}`
} as const;

// Cache TTL constants (in milliseconds)
export const CACHE_TTL = {
  SHORT: 1 * 60 * 1000,      // 1 minute
  MEDIUM: 5 * 60 * 1000,     // 5 minutes
  LONG: 30 * 60 * 1000,      // 30 minutes
  VERY_LONG: 2 * 60 * 60 * 1000  // 2 hours
} as const;

// Utility functions for common caching patterns
export const cacheUtils = {
  // Get or fetch pattern
  async getOrFetch<T>(
    key: string,
    fetchFn: () => Promise<T>,
    ttl?: number
  ): Promise<T> {
    // Try to get from cache first
    const cached = cache.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // Fetch fresh data
    const data = await fetchFn();
    cache.set(key, data, ttl);
    return data;
  },

  // Invalidate related cache entries
  invalidatePattern(pattern: string): number {
    let removedCount = 0;
    const regex = new RegExp(pattern);

    for (const key of cache['cache'].keys()) {
      if (regex.test(key)) {
        cache.delete(key);
        removedCount++;
      }
    }

    return removedCount;
  },

  // Preload data into cache
  preload<T>(key: string, data: T, ttl?: number): void {
    cache.set(key, data, ttl);
  },

  // Batch cache operations
  setBatch<T>(entries: Array<{ key: string; data: T; ttl?: number }>): void {
    entries.forEach(({ key, data, ttl }) => {
      cache.set(key, data, ttl);
    });
  }
};

// Auto cleanup expired items every 10 minutes
setInterval(() => {
  const removed = cache.cleanup();
  if (removed > 0) {
    console.log(`Cache cleanup: removed ${removed} expired items`);
  }
}, 10 * 60 * 1000);

// Browser storage cache for persistence
export const persistentCache = {
  set<T>(key: string, data: T, ttl?: number): void {
    try {
      const item = {
        data,
        timestamp: Date.now(),
        ttl: ttl || 24 * 60 * 60 * 1000 // Default 24 hours for persistent cache
      };
      localStorage.setItem(`marcat_cache_${key}`, JSON.stringify(item));
    } catch (error) {
      console.warn('Failed to save to localStorage:', error);
    }
  },

  get<T>(key: string): T | null {
    try {
      const stored = localStorage.getItem(`marcat_cache_${key}`);
      if (!stored) return null;

      const item = JSON.parse(stored);
      
      // Check if expired
      if (Date.now() - item.timestamp > item.ttl) {
        localStorage.removeItem(`marcat_cache_${key}`);
        return null;
      }

      return item.data as T;
    } catch (error) {
      console.warn('Failed to read from localStorage:', error);
      return null;
    }
  },

  delete(key: string): void {
    try {
      localStorage.removeItem(`marcat_cache_${key}`);
    } catch (error) {
      console.warn('Failed to delete from localStorage:', error);
    }
  },

  clear(): void {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith('marcat_cache_'));
      keys.forEach(key => localStorage.removeItem(key));
    } catch (error) {
      console.warn('Failed to clear localStorage cache:', error);
    }
  }
};

export default cache;
