
import { useState, useCallback, type FC } from 'react';
import { GoogleGenAI } from "@google/genai";
import type { Product } from '../types';
import { products as allProducts } from '../data';
import Icon from './Icon';

const SuggestedProductCard: FC<{ product: Product; onSelect: (id: string) => void; }> = ({ product, onSelect }) => {
    return (
        <div 
            onClick={() => onSelect(product.id)} 
            className="cursor-pointer group relative"
            aria-label={`View details for ${product.name}`}
        >
            <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-md bg-gray-200 lg:aspect-none group-hover:opacity-75 h-48 sm:h-56 md:h-64">
                <img 
                    src={product.variants[0].imageUrl} 
                    alt={product.name} 
                    className="h-full w-full object-cover object-center" 
                />
            </div>
            <div className="mt-2 flex justify-between">
                <div>
                    <h3 className="text-sm text-gray-700">
                        <span aria-hidden="true" className="absolute inset-0" />
                        {product.name}
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">{product.category}</p>
                </div>
                <p className="text-sm font-medium text-gray-900">${product.basePrice.toFixed(2)}</p>
            </div>
        </div>
    );
};

interface StyleAssistantProps {
    product: Product;
    onProductSelect: (id: string) => void;
}

const StyleAssistant: FC<StyleAssistantProps> = ({ product, onProductSelect }) => {
    const [suggestions, setSuggestions] = useState<Product[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const generateStyle = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        setSuggestions([]);

        try {
            const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });
            
            const availableProducts = allProducts
                .filter(p => p.id !== product.id)
                .map(({ id, name, category, description, tags }) => ({ id, name, category, description, tags }));

            const prompt = `You are an expert fashion stylist for a men's clothing marketplace called Marcat. Your task is to recommend 2 to 3 complementary items to create a full outfit based on a single product. The main product is: Name: ${product.name}, Category: ${product.category}, Description: ${product.description}, Tags: ${product.tags.join(', ')}. Here is a list of available products to choose from: ${JSON.stringify(availableProducts)}. Based on the main product, select 2 or 3 items from the available list that would create a stylish and cohesive outfit. Do not suggest items of the same category as the main product. Respond ONLY with a JSON object containing a key "suggestedProductIds" which is an array of the product IDs for your recommended items. For example: {"suggestedProductIds": ["prod-5", "prod-8"]}.`;

            const response = await ai.models.generateContent({
                model: 'gemini-2.5-flash-preview-04-17',
                contents: prompt,
                config: {
                    responseMimeType: "application/json",
                }
            });

            let jsonStr = response.text.trim();
            const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
            const match = jsonStr.match(fenceRegex);
            if (match && match[2]) {
                jsonStr = match[2].trim();
            }

            const parsedData = JSON.parse(jsonStr);
            
            if (parsedData && parsedData.suggestedProductIds && Array.isArray(parsedData.suggestedProductIds)) {
                const suggestedIds = parsedData.suggestedProductIds as string[];
                const foundProducts = allProducts.filter(p => suggestedIds.includes(p.id));
                if (foundProducts.length === 0) {
                     throw new Error("AI returned valid IDs, but no products were found. Retrying might help.");
                }
                setSuggestions(foundProducts);
            } else {
                throw new Error("Invalid response format from AI.");
            }

        } catch (e) {
            console.error("Error generating style advice:", e);
            setError("Sorry, our stylist is a bit busy. Please try again in a moment.");
        } finally {
            setIsLoading(false);
        }
    }, [product]);

    return (
        <div className="mt-16 bg-white rounded-lg shadow-2xl p-4 sm:p-8 lg:p-12">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex items-center gap-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <Icon name="sparkles" className="w-7 h-7 text-indigo-600"/>
                    </div>
                    <div>
                        <h2 className="text-2xl font-bold tracking-tight text-gray-900">Complete the Look with AI</h2>
                        <p className="mt-1 text-gray-600">Let our AI stylist create an outfit for you.</p>
                    </div>
                </div>
                 {!isLoading && suggestions.length === 0 && (
                     <button
                        onClick={generateStyle}
                        className="inline-flex items-center justify-center gap-2 rounded-md border border-transparent bg-indigo-600 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                    >
                         <Icon name="sparkles" className="w-5 h-5"/>
                        Generate Outfit
                    </button>
                )}
            </div>
            
            <div className="mt-8">
                {isLoading && (
                    <div className="flex items-center justify-center py-10">
                        <div className="flex items-center gap-3 text-gray-600">
                            <div className="w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
                            <span className="font-medium text-lg">Our stylist is thinking...</span>
                        </div>
                    </div>
                )}

                {error && (
                    <div className="text-center py-10 px-4 bg-red-50 rounded-lg">
                        <p className="text-lg font-medium text-red-700">{error}</p>
                         <button
                            onClick={generateStyle}
                            className="mt-4 inline-flex items-center gap-2 rounded-md border border-transparent bg-red-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-red-700"
                        >
                            Try Again
                        </button>
                    </div>
                )}
                
                {!isLoading && suggestions.length > 0 && (
                     <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Our AI Recommends:</h3>
                         <div className="grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3">
                            {suggestions.map(p => (
                                <SuggestedProductCard key={p.id} product={p} onSelect={onProductSelect} />
                            ))}
                        </div>
                        <div className="mt-8 text-center">
                             <button
                                onClick={generateStyle}
                                className="font-semibold text-indigo-600 hover:text-indigo-500 transition-colors"
                            >
                                Get new recommendations
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default StyleAssistant;