import { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { vi } from 'vitest';
import { AuthProvider } from '../../context/AuthContext';
import { CartProvider } from '../../context/CartContext';
import { WishlistProvider } from '../../context/WishlistContext';
import { ComparisonProvider } from '../../context/ComparisonContext';
import type { User } from '../../types';

// Mock user data for testing
export const mockUsers = {
  customer: {
    id: 'test-customer',
    name: 'Test Customer',
    email: '<EMAIL>',
    role: 'customer' as const,
  },
  admin: {
    id: 'test-admin',
    name: 'Test Admin',
    email: '<EMAIL>',
    role: 'admin' as const,
  },
};

// Mock product data for testing
export const mockProduct = {
  id: 'test-product-1',
  name: 'Test Product',
  description: 'A test product for unit testing',
  category: 'Shirts',
  storeId: 'test-store-1',
  tags: ['test', 'shirt'],
  variants: [
    {
      id: 'test-variant-1',
      color: 'Blue',
      size: 'M',
      price: 29.99,
      originalPrice: 39.99,
      stock: 10,
      imageUrl: 'https://example.com/test-image.jpg',
    },
  ],
  offer: {
    type: 'percentage' as const,
    value: 25,
    validUntil: new Date(Date.now() + 86400000), // 24 hours from now
  },
};

// Mock store data for testing
export const mockStore = {
  id: 'test-store-1',
  name: 'Test Store',
  logoUrl: 'https://example.com/test-logo.jpg',
  rating: 4.5,
};

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialUser?: User | null;
  preloadedState?: {
    cart?: any;
    wishlist?: any;
    comparison?: any;
  };
}

// Custom render function that includes all providers
function customRender(
  ui: ReactElement,
  {
    initialUser = null,
    preloadedState = {},
    ...renderOptions
  }: CustomRenderOptions = {}
) {
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <AuthProvider initialUser={initialUser}>
        <ComparisonProvider>
          <WishlistProvider>
            <CartProvider>
              {children}
            </CartProvider>
          </WishlistProvider>
        </ComparisonProvider>
      </AuthProvider>
    );
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// Mock implementations for common hooks
export const mockAuthContext = {
  currentUser: null,
  login: vi.fn(),
  register: vi.fn(),
  logout: vi.fn(),
  isLoading: false,
};

export const mockCartContext = {
  cartItems: [],
  addToCart: vi.fn(),
  updateQuantity: vi.fn(),
  removeFromCart: vi.fn(),
  clearCart: vi.fn(),
  totalCartItems: 0,
};

export const mockWishlistContext = {
  wishlistItems: [],
  addToWishlist: vi.fn(),
  removeFromWishlist: vi.fn(),
  isInWishlist: vi.fn().mockReturnValue(false),
};

export const mockComparisonContext = {
  comparisonItems: [],
  addToComparison: vi.fn(),
  removeFromComparison: vi.fn(),
  clearComparison: vi.fn(),
  isInComparison: vi.fn().mockReturnValue(false),
  comparisonCount: 0,
};

// Utility functions for testing
export const createMockEvent = (overrides = {}) => ({
  preventDefault: vi.fn(),
  stopPropagation: vi.fn(),
  target: { value: '' },
  ...overrides,
});

export const createMockFile = (name = 'test.jpg', type = 'image/jpeg') => {
  return new File(['test'], name, { type });
};

// Wait for async operations in tests
export const waitForLoadingToFinish = () => {
  return new Promise((resolve) => setTimeout(resolve, 0));
};

// Mock intersection observer entry
export const createMockIntersectionObserverEntry = (isIntersecting = true) => ({
  isIntersecting,
  target: document.createElement('div'),
  intersectionRatio: isIntersecting ? 1 : 0,
  boundingClientRect: {} as DOMRectReadOnly,
  intersectionRect: {} as DOMRectReadOnly,
  rootBounds: {} as DOMRectReadOnly,
  time: Date.now(),
});

// Re-export everything from React Testing Library
export * from '@testing-library/react';
export { customRender as render };
