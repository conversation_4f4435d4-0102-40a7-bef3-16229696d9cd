import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { cache, CACHE_KEYS, CACHE_TTL, cacheUtils, persistentCache } from '../../utils/cache';

describe('Cache', () => {
  beforeEach(() => {
    cache.clear();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Basic cache operations', () => {
    it('sets and gets data correctly', () => {
      const testData = { id: 1, name: 'Test' };
      cache.set('test-key', testData);

      const retrieved = cache.get('test-key');
      expect(retrieved).toEqual(testData);
    });

    it('returns null for non-existent keys', () => {
      const result = cache.get('non-existent');
      expect(result).toBeNull();
    });

    it('checks if key exists correctly', () => {
      cache.set('test-key', 'test-data');
      
      expect(cache.has('test-key')).toBe(true);
      expect(cache.has('non-existent')).toBe(false);
    });

    it('deletes data correctly', () => {
      cache.set('test-key', 'test-data');
      expect(cache.has('test-key')).toBe(true);

      const deleted = cache.delete('test-key');
      expect(deleted).toBe(true);
      expect(cache.has('test-key')).toBe(false);
    });

    it('clears all data', () => {
      cache.set('key1', 'data1');
      cache.set('key2', 'data2');

      cache.clear();

      expect(cache.has('key1')).toBe(false);
      expect(cache.has('key2')).toBe(false);
    });
  });

  describe('TTL (Time To Live) functionality', () => {
    it('respects custom TTL', () => {
      const shortTTL = 1000; // 1 second
      cache.set('test-key', 'test-data', shortTTL);

      expect(cache.get('test-key')).toBe('test-data');

      // Fast forward time beyond TTL
      vi.advanceTimersByTime(shortTTL + 1);

      expect(cache.get('test-key')).toBeNull();
      expect(cache.has('test-key')).toBe(false);
    });

    it('uses default TTL when not specified', () => {
      cache.set('test-key', 'test-data');

      // Fast forward to just before default TTL (5 minutes)
      vi.advanceTimersByTime(5 * 60 * 1000 - 1);
      expect(cache.get('test-key')).toBe('test-data');

      // Fast forward past default TTL
      vi.advanceTimersByTime(2);
      expect(cache.get('test-key')).toBeNull();
    });

    it('removes expired items when checking existence', () => {
      cache.set('test-key', 'test-data', 1000);

      vi.advanceTimersByTime(1001);

      expect(cache.has('test-key')).toBe(false);
      // Item should be removed from internal cache
      expect(cache['cache'].has('test-key')).toBe(false);
    });
  });

  describe('Cache statistics', () => {
    it('provides accurate statistics', () => {
      cache.set('valid1', 'data1', 10000);
      cache.set('valid2', 'data2', 10000);
      cache.set('expired1', 'data3', 1000);
      cache.set('expired2', 'data4', 1000);

      // Fast forward to expire some items
      vi.advanceTimersByTime(1001);

      const stats = cache.getStats();

      expect(stats.totalItems).toBe(4);
      expect(stats.validItems).toBe(2);
      expect(stats.expiredItems).toBe(2);
      expect(stats.hitRate).toBe(0.5);
    });

    it('handles empty cache statistics', () => {
      const stats = cache.getStats();

      expect(stats.totalItems).toBe(0);
      expect(stats.validItems).toBe(0);
      expect(stats.expiredItems).toBe(0);
      expect(stats.hitRate).toBe(0);
    });
  });

  describe('Cache cleanup', () => {
    it('removes expired items during cleanup', () => {
      cache.set('valid', 'data1', 10000);
      cache.set('expired1', 'data2', 1000);
      cache.set('expired2', 'data3', 1000);

      vi.advanceTimersByTime(1001);

      const removedCount = cache.cleanup();

      expect(removedCount).toBe(2);
      expect(cache.has('valid')).toBe(true);
      expect(cache.has('expired1')).toBe(false);
      expect(cache.has('expired2')).toBe(false);
    });

    it('returns 0 when no items need cleanup', () => {
      cache.set('valid1', 'data1', 10000);
      cache.set('valid2', 'data2', 10000);

      const removedCount = cache.cleanup();

      expect(removedCount).toBe(0);
    });
  });
});

describe('Cache utilities', () => {
  beforeEach(() => {
    cache.clear();
  });

  describe('getOrFetch', () => {
    it('returns cached data when available', async () => {
      const testData = { id: 1, name: 'Cached' };
      cache.set('test-key', testData);

      const fetchFn = vi.fn().mockResolvedValue({ id: 2, name: 'Fresh' });

      const result = await cacheUtils.getOrFetch('test-key', fetchFn);

      expect(result).toEqual(testData);
      expect(fetchFn).not.toHaveBeenCalled();
    });

    it('fetches and caches data when not in cache', async () => {
      const freshData = { id: 2, name: 'Fresh' };
      const fetchFn = vi.fn().mockResolvedValue(freshData);

      const result = await cacheUtils.getOrFetch('test-key', fetchFn, 5000);

      expect(result).toEqual(freshData);
      expect(fetchFn).toHaveBeenCalledOnce();
      expect(cache.get('test-key')).toEqual(freshData);
    });
  });

  describe('invalidatePattern', () => {
    it('removes keys matching pattern', () => {
      cache.set('user_1_profile', 'data1');
      cache.set('user_2_profile', 'data2');
      cache.set('product_1', 'data3');
      cache.set('user_1_orders', 'data4');

      const removedCount = cacheUtils.invalidatePattern('user_.*_profile');

      expect(removedCount).toBe(2);
      expect(cache.has('user_1_profile')).toBe(false);
      expect(cache.has('user_2_profile')).toBe(false);
      expect(cache.has('product_1')).toBe(true);
      expect(cache.has('user_1_orders')).toBe(true);
    });
  });

  describe('setBatch', () => {
    it('sets multiple cache entries at once', () => {
      const entries = [
        { key: 'key1', data: 'data1', ttl: 1000 },
        { key: 'key2', data: 'data2', ttl: 2000 },
        { key: 'key3', data: 'data3' },
      ];

      cacheUtils.setBatch(entries);

      expect(cache.get('key1')).toBe('data1');
      expect(cache.get('key2')).toBe('data2');
      expect(cache.get('key3')).toBe('data3');
    });
  });
});

describe('Persistent cache', () => {
  const mockLocalStorage = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    key: vi.fn(),
    length: 0,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });
  });

  it('sets data in localStorage', () => {
    const testData = { id: 1, name: 'Test' };
    persistentCache.set('test-key', testData, 5000);

    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'marcat_cache_test-key',
      expect.stringContaining('"data":{"id":1,"name":"Test"}')
    );
  });

  it('gets data from localStorage', () => {
    const testData = { id: 1, name: 'Test' };
    const cacheItem = {
      data: testData,
      timestamp: Date.now(),
      ttl: 5000,
    };

    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(cacheItem));

    const result = persistentCache.get('test-key');

    expect(result).toEqual(testData);
    expect(mockLocalStorage.getItem).toHaveBeenCalledWith('marcat_cache_test-key');
  });

  it('returns null for expired data', () => {
    const testData = { id: 1, name: 'Test' };
    const expiredItem = {
      data: testData,
      timestamp: Date.now() - 10000, // 10 seconds ago
      ttl: 5000, // 5 second TTL
    };

    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(expiredItem));

    const result = persistentCache.get('test-key');

    expect(result).toBeNull();
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('marcat_cache_test-key');
  });

  it('handles localStorage errors gracefully', () => {
    mockLocalStorage.setItem.mockImplementation(() => {
      throw new Error('Storage quota exceeded');
    });

    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

    persistentCache.set('test-key', 'test-data');

    expect(consoleSpy).toHaveBeenCalledWith('Failed to save to localStorage:', expect.any(Error));
    consoleSpy.mockRestore();
  });
});

describe('Cache constants', () => {
  it('provides correct cache keys', () => {
    expect(CACHE_KEYS.PRODUCTS).toBe('products');
    expect(CACHE_KEYS.PRODUCT_DETAIL('123')).toBe('product_detail_123');
    expect(CACHE_KEYS.REVIEWS('456')).toBe('reviews_456');
    expect(CACHE_KEYS.USER_PROFILE('user1')).toBe('user_profile_user1');
  });

  it('provides correct TTL values', () => {
    expect(CACHE_TTL.SHORT).toBe(1 * 60 * 1000);
    expect(CACHE_TTL.MEDIUM).toBe(5 * 60 * 1000);
    expect(CACHE_TTL.LONG).toBe(30 * 60 * 1000);
    expect(CACHE_TTL.VERY_LONG).toBe(2 * 60 * 60 * 1000);
  });
});
