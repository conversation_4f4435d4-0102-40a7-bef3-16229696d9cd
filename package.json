{"name": "marcat---men's-clothing-marketplace", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "cypress:open": "cypress open", "cypress:run": "cypress run", "e2e": "start-server-and-test dev http://localhost:5173 cypress:run", "e2e:open": "start-server-and-test dev http://localhost:5173 cypress:open", "test:all": "npm run test:run && npm run e2e", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "@google/genai": "^1.8.0"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "@testing-library/react": "^16.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.5.2", "@vitejs/plugin-react": "^4.3.4", "@vitest/ui": "^2.1.8", "@vitest/coverage-v8": "^2.1.8", "cypress": "^13.15.0", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-jsx-a11y": "^6.10.2", "jsdom": "^26.0.0", "start-server-and-test": "^2.0.8", "typescript": "~5.7.2", "vite": "^6.2.0", "vitest": "^2.1.8"}}