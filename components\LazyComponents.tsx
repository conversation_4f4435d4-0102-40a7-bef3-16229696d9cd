import { lazy } from 'react';

// Lazy load heavy components to improve initial bundle size
export const LazyProductDetail = lazy(() => import('./ProductDetail'));
export const LazyCheckout = lazy(() => import('./Checkout'));
export const LazyPersonalShopper = lazy(() => import('./PersonalShopper'));
export const LazyProductComparison = lazy(() => import('./ProductComparison'));
export const LazyOrderTracking = lazy(() => import('./OrderTracking'));
export const LazyLoyaltyProgram = lazy(() => import('./LoyaltyProgram'));
export const LazyStyleRecommendations = lazy(() => import('./StyleRecommendations'));
export const LazyAdminDashboard = lazy(() => import('./admin/AdminDashboard'));

// Admin sub-components
export const LazyProductManagement = lazy(() => import('./admin/ProductManagement'));
export const LazyOrderManagement = lazy(() => import('./admin/OrderManagement'));
export const LazyUserManagement = lazy(() => import('./admin/UserManagement'));
export const LazyAnalytics = lazy(() => import('./admin/Analytics'));

// Loading fallback component
export const ComponentLoader = () => (
  <div className="flex items-center justify-center min-h-[400px]">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
  </div>
);

// Error boundary for lazy components
import { Component, type ReactNode, type ErrorInfo } from 'react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export class LazyErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Lazy component loading error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="flex items-center justify-center min-h-[400px] bg-red-50 rounded-lg">
          <div className="text-center p-6">
            <div className="text-red-600 text-lg font-semibold mb-2">
              Failed to load component
            </div>
            <div className="text-red-500 text-sm mb-4">
              {this.state.error?.message || 'An unexpected error occurred'}
            </div>
            <button
              onClick={() => this.setState({ hasError: false, error: undefined })}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
