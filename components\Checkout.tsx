
import { useState, useMemo, useEffect, type FC, type FormEvent } from 'react';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';
import { products as allProducts } from '../data';
import type { PromoCode } from '../types';
import Icon from './Icon';
import PromoCodeInput from './PromoCodeInput';

interface CheckoutProps {
    onBackToList: () => void;
    onOrderPlaced: () => void;
}

const Checkout: FC<CheckoutProps> = ({ onBackToList, onOrderPlaced }) => {
    const { cartItems, clearCart } = useCart();
    const { currentUser } = useAuth();
    
    const [email, setEmail] = useState('');
    const [name, setName] = useState('');
    const [address, setAddress] = useState('');
    const [city, setCity] = useState('');
    const [country, setCountry] = useState('United States');
    const [appliedPromo, setAppliedPromo] = useState<PromoCode | undefined>();
    const [promoDiscount, setPromoDiscount] = useState(0);

    useEffect(() => {
        if (currentUser) {
            setEmail(currentUser.email);
            setName(currentUser.name);
        }
    }, [currentUser]);
    
    const cartDetails = useMemo(() => {
        return cartItems.map(item => {
            const product = allProducts.find(p => p.id === item.productId);
            if (!product) return null;
            const variant = product.variants.find(v => v.id === item.variantId);
            if (!variant) return null;
            return {
                ...item,
                name: product.name,
                image: variant.imageUrl,
            };
        }).filter(Boolean);
    }, [cartItems]);

    const subtotal = useMemo(() => {
        return cartDetails.reduce((total, item) => {
            if (!item) return total;
            return total + item.unitPrice * item.quantity;
        }, 0);
    }, [cartDetails]);

    const shippingCost = subtotal > 50 ? 0 : 5.99;
    const total = subtotal + shippingCost - promoDiscount;

    const handlePromoApplied = (promoCode: PromoCode, discount: number) => {
        setAppliedPromo(promoCode);
        setPromoDiscount(discount);
    };

    const handlePromoRemoved = () => {
        setAppliedPromo(undefined);
        setPromoDiscount(0);
    };

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        // Here you would typically process the payment
        console.log('Placing order with details:', {
            name, email, address, city, country, items: cartDetails
        });
        clearCart();
        onOrderPlaced();
    };

    if (cartItems.length === 0) {
        return (
            <div className="text-center py-20">
                <Icon name="cart" className="w-16 h-16 mx-auto text-gray-300"/>
                <h3 className="mt-4 text-2xl font-bold text-gray-900">Your cart is empty</h3>
                <p className="mt-2 text-md text-gray-500">You can't proceed to checkout with an empty cart.</p>
                <button onClick={onBackToList} className="mt-6 inline-flex items-center justify-center gap-2 rounded-md border border-transparent bg-indigo-600 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-indigo-700">
                    Go Shopping
                </button>
            </div>
        )
    }

    return (
        <div>
            <button onClick={onBackToList} className="mb-8 inline-flex items-center gap-2 text-gray-600 hover:text-gray-900 font-semibold">
                <Icon name="arrow-left" className="w-5 h-5"/>
                Continue Shopping
            </button>
        
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-12">
                {/* Shipping and Payment Info */}
                <div className="lg:col-span-1">
                    <form onSubmit={handleSubmit}>
                        <div className="space-y-8">
                            <div className="p-8 bg-white rounded-lg shadow-lg">
                                <h2 className="text-2xl font-bold text-gray-900">Contact Information</h2>
                                <div className="mt-6 grid grid-cols-1 gap-y-6">
                                    <div>
                                        <label htmlFor="email-address" className="block text-sm font-medium text-gray-700">Email address</label>
                                        <input type="email" id="email-address" value={email} onChange={e => setEmail(e.target.value)} autoComplete="email" className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" required />
                                    </div>
                                </div>
                            </div>
                            
                            <div className="p-8 bg-white rounded-lg shadow-lg">
                                <h2 className="text-2xl font-bold text-gray-900">Shipping Information</h2>
                                <div className="mt-6 grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-4">
                                    <div className="sm:col-span-2">
                                        <label htmlFor="name" className="block text-sm font-medium text-gray-700">Full name</label>
                                        <input type="text" id="name" value={name} onChange={e => setName(e.target.value)} autoComplete="name" className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" required/>
                                    </div>
                                    <div className="sm:col-span-2">
                                        <label htmlFor="address" className="block text-sm font-medium text-gray-700">Address</label>
                                        <input type="text" id="address" value={address} onChange={e => setAddress(e.target.value)} autoComplete="street-address" className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" required />
                                    </div>
                                    <div>
                                        <label htmlFor="city" className="block text-sm font-medium text-gray-700">City</label>
                                        <input type="text" id="city" value={city} onChange={e => setCity(e.target.value)} autoComplete="address-level2" className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" required />
                                    </div>
                                    <div>
                                        <label htmlFor="country" className="block text-sm font-medium text-gray-700">Country</label>
                                        <select id="country" value={country} onChange={e => setCountry(e.target.value)} autoComplete="country-name" className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" required>
                                            <option>United States</option>
                                            <option>Canada</option>
                                            <option>Mexico</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="p-8 bg-white rounded-lg shadow-lg">
                                 <h2 className="text-2xl font-bold text-gray-900">Payment Details</h2>
                                 <div className="mt-6 flex items-center gap-4 p-4 rounded-md bg-indigo-50 border border-indigo-200">
                                     <Icon name="credit-card" className="w-8 h-8 text-indigo-500 flex-shrink-0" />
                                     <p className="text-sm text-indigo-800">This is a demo store. No real payment will be processed. You can safely place the order.</p>
                                 </div>
                            </div>
                        </div>

                        <div className="mt-10">
                             <button type="submit" className="flex w-full items-center justify-center rounded-md border border-transparent bg-indigo-600 px-6 py-4 text-base font-medium text-white shadow-lg hover:bg-indigo-700">
                                Place Order
                            </button>
                        </div>
                    </form>
                </div>

                {/* Order Summary */}
                <div className="lg:col-span-1">
                    <div className="sticky top-28">
                         <div className="p-8 bg-white rounded-lg shadow-lg">
                            <h2 className="text-2xl font-bold text-gray-900">Order summary</h2>
                            <ul role="list" className="mt-6 divide-y divide-gray-200">
                                {cartDetails.map(item => item && (
                                    <li key={item.variantId} className="flex py-6">
                                        <div className="h-24 w-24 flex-shrink-0 overflow-hidden rounded-md border border-gray-200">
                                            <img src={item.image} alt={item.name} className="h-full w-full object-cover object-center" />
                                        </div>
                                        <div className="ml-4 flex flex-1 flex-col">
                                            <div>
                                                <h3 className="text-md font-medium text-gray-900">{item.name}</h3>
                                                <p className="text-sm text-gray-500">Qty: {item.quantity}</p>
                                            </div>
                                            <div className="flex flex-1 items-end justify-end">
                                                <p className="text-md font-medium text-gray-900">${(item.unitPrice * item.quantity).toFixed(2)}</p>
                                            </div>
                                        </div>
                                    </li>
                                ))}
                            </ul>

                            {/* Promo Code Section */}
                            <div className="mt-6 border-t border-gray-200 pt-6">
                                <h3 className="text-lg font-medium text-gray-900 mb-4">Promo Code</h3>
                                <PromoCodeInput
                                    onPromoApplied={handlePromoApplied}
                                    onPromoRemoved={handlePromoRemoved}
                                    appliedPromo={appliedPromo}
                                    orderTotal={subtotal}
                                />
                            </div>

                            <div className="mt-6 space-y-2 border-t border-gray-200 pt-6">
                                <div className="flex items-center justify-between text-md text-gray-600">
                                    <p>Subtotal</p>
                                    <p>${subtotal.toFixed(2)}</p>
                                </div>
                                <div className="flex items-center justify-between text-md text-gray-600">
                                    <p>Shipping</p>
                                    <p>${shippingCost.toFixed(2)}</p>
                                </div>
                                {promoDiscount > 0 && (
                                    <div className="flex items-center justify-between text-md text-green-600">
                                        <p>Discount ({appliedPromo?.code})</p>
                                        <p>-${promoDiscount.toFixed(2)}</p>
                                    </div>
                                )}
                                <div className="flex items-center justify-between text-lg font-bold text-gray-900 border-t border-gray-200 pt-4 mt-4">
                                    <p>Total</p>
                                    <p>${total.toFixed(2)}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Checkout;
