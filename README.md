# Marcat - Men's Clothing Marketplace

A modern, comprehensive e-commerce platform for men's clothing with AI-powered features, advanced admin capabilities, and enterprise-grade performance optimizations.

## 🚀 Features

### Core E-commerce Features
- **Product Browsing & Search** - Advanced filtering, search, and categorization
- **Shopping Cart** - Persistent cart with quantity management
- **User Authentication** - Secure login/registration system
- **Checkout Process** - Complete order processing workflow
- **Wishlist & Comparison** - Save favorites and compare products
- **Order Tracking** - Real-time order status updates

### Advanced Features
- **AI Personal Shopper** - Google Gemini AI-powered shopping assistant
- **Style Recommendations** - Personalized outfit suggestions
- **Loyalty Program** - Points-based rewards system
- **Visual Search** - Image-based product discovery
- **Promotional Codes** - Discount and coupon system
- **Size Guide** - Interactive sizing assistance

### Admin Dashboard
- **Product Management** - CRUD operations for inventory
- **Order Management** - Order processing and fulfillment
- **User Management** - Customer and admin account management
- **Analytics Dashboard** - Sales metrics and performance insights

### Performance & Technical
- **Lazy Loading** - Code splitting and on-demand component loading
- **Caching System** - In-memory and persistent caching with TTL
- **Performance Monitoring** - Real-time performance tracking
- **Optimized Images** - Responsive images with lazy loading
- **Error Boundaries** - Robust error handling

## 🛠️ Technologies Used

- **Frontend**: React 19.1.0, TypeScript, Vite
- **Styling**: Tailwind CSS
- **AI Integration**: Google Gemini AI
- **State Management**: React Context API
- **Testing**: Vitest, Testing Library, Cypress
- **Performance**: Custom caching, lazy loading, image optimization

## 📦 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd marcat
```

2. **Install dependencies:**
```bash
npm install
```

3. **Set up environment variables:**
```bash
# Create .env.local file
echo "GEMINI_API_KEY=your_gemini_api_key_here" > .env.local
```

4. **Start the development server:**
```bash
npm run dev
```

5. **Open your browser:**
Navigate to [http://localhost:5173](http://localhost:5173)

## 🧪 Testing

### Unit & Integration Tests
```bash
# Run all tests
npm run test

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage

# Watch mode for development
npm run test:watch
```

### End-to-End Tests
```bash
# Run E2E tests headlessly
npm run e2e

# Open Cypress UI
npm run e2e:open

# Run all tests (unit + E2E)
npm run test:all
```

### Test Coverage
The project maintains high test coverage with thresholds:
- **Branches**: 80%
- **Functions**: 80%
- **Lines**: 80%
- **Statements**: 80%

## 🏗️ Project Structure

```
src/
├── components/           # React components
│   ├── admin/           # Admin dashboard components
│   ├── LazyComponents.tsx # Lazy-loaded component definitions
│   └── OptimizedImage.tsx # Performance-optimized image component
├── context/             # React context providers
├── utils/               # Utility functions
│   ├── cache.ts         # Caching system
│   └── performance.ts   # Performance monitoring
├── data/                # Mock data and types
├── types/               # TypeScript definitions
└── __tests__/           # Test files
    ├── components/      # Component tests
    ├── context/         # Context tests
    └── integration/     # Integration tests
```

## 🚀 Performance Features

### Lazy Loading
- All major components are lazy-loaded to reduce initial bundle size
- Components load on-demand with proper error boundaries
- Suspense fallbacks provide smooth loading experience

### Caching System
- In-memory cache with TTL (Time To Live) support
- Persistent localStorage cache for long-term data
- Automatic cache invalidation and cleanup
- Batch operations for efficient data management

### Image Optimization
- Responsive images with automatic srcSet generation
- Lazy loading with Intersection Observer
- WebP/AVIF format detection and fallbacks
- Placeholder and blur effects during loading

### Performance Monitoring
- Real-time Web Vitals tracking (LCP, FID, CLS, FCP)
- Memory usage monitoring and leak detection
- Bundle size analysis and optimization
- Component render time measurement

## 🔧 Development

### Code Quality
```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Type checking
npm run type-check
```

### Build & Deploy
```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

## 📱 Features Overview

### User Experience
- **Responsive Design** - Mobile-first approach with breakpoint optimization
- **Accessibility** - WCAG compliant with keyboard navigation support
- **Performance** - Sub-3-second load times with lazy loading
- **Error Handling** - Graceful error boundaries and fallback UI

### E-commerce Functionality
- **Product Catalog** - Advanced filtering, sorting, and search
- **Shopping Cart** - Persistent cart with real-time updates
- **Checkout** - Streamlined checkout process with validation
- **Order Management** - Complete order lifecycle tracking

### AI Integration
- **Personal Shopper** - Conversational AI for product recommendations
- **Style Matching** - AI-powered outfit coordination
- **Visual Search** - Image-based product discovery
- **Personalization** - User behavior-based recommendations

### Admin Capabilities
- **Dashboard Analytics** - Real-time sales and performance metrics
- **Inventory Management** - Product CRUD with bulk operations
- **Order Processing** - Order fulfillment and status management
- **User Administration** - Customer and admin account management

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Google Gemini AI for intelligent shopping assistance
- React team for the excellent framework
- Tailwind CSS for utility-first styling
- Testing Library for comprehensive testing utilities
