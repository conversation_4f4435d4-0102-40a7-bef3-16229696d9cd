// Performance monitoring utilities

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  // Start timing a metric
  startTiming(name: string, metadata?: Record<string, any>): void {
    const metric: PerformanceMetric = {
      name,
      startTime: performance.now(),
      metadata
    };
    this.metrics.set(name, metric);
  }

  // End timing a metric
  endTiming(name: string): number | null {
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric "${name}" not found`);
      return null;
    }

    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;

    // Log slow operations (> 100ms)
    if (metric.duration > 100) {
      console.warn(`Slow operation detected: ${name} took ${metric.duration.toFixed(2)}ms`);
    }

    return metric.duration;
  }

  // Get metric data
  getMetric(name: string): PerformanceMetric | null {
    return this.metrics.get(name) || null;
  }

  // Get all metrics
  getAllMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  // Clear metrics
  clearMetrics(): void {
    this.metrics.clear();
  }

  // Initialize performance observers
  private initializeObservers(): void {
    if (typeof window === 'undefined') return;

    try {
      // Observe navigation timing
      const navObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            this.logNavigationMetrics(navEntry);
          }
        });
      });
      navObserver.observe({ entryTypes: ['navigation'] });
      this.observers.push(navObserver);

      // Observe resource loading
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'resource') {
            this.logResourceMetrics(entry as PerformanceResourceTiming);
          }
        });
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);

      // Observe largest contentful paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        console.log('LCP:', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(lcpObserver);

      // Observe first input delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          console.log('FID:', entry.processingStart - entry.startTime);
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.push(fidObserver);

    } catch (error) {
      console.warn('Performance observers not supported:', error);
    }
  }

  private logNavigationMetrics(entry: PerformanceNavigationTiming): void {
    const metrics = {
      'DNS Lookup': entry.domainLookupEnd - entry.domainLookupStart,
      'TCP Connection': entry.connectEnd - entry.connectStart,
      'Request': entry.responseStart - entry.requestStart,
      'Response': entry.responseEnd - entry.responseStart,
      'DOM Processing': entry.domComplete - entry.domLoading,
      'Load Complete': entry.loadEventEnd - entry.loadEventStart,
      'Total Load Time': entry.loadEventEnd - entry.navigationStart
    };

    console.group('Navigation Performance Metrics');
    Object.entries(metrics).forEach(([name, duration]) => {
      if (duration > 0) {
        console.log(`${name}: ${duration.toFixed(2)}ms`);
      }
    });
    console.groupEnd();
  }

  private logResourceMetrics(entry: PerformanceResourceTiming): void {
    const duration = entry.responseEnd - entry.startTime;
    
    // Log slow resources (> 500ms)
    if (duration > 500) {
      console.warn(`Slow resource: ${entry.name} took ${duration.toFixed(2)}ms`);
    }
  }

  // Disconnect all observers
  disconnect(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions
export const measureAsync = async <T>(
  name: string,
  asyncFn: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> => {
  performanceMonitor.startTiming(name, metadata);
  try {
    const result = await asyncFn();
    return result;
  } finally {
    performanceMonitor.endTiming(name);
  }
};

export const measureSync = <T>(
  name: string,
  syncFn: () => T,
  metadata?: Record<string, any>
): T => {
  performanceMonitor.startTiming(name, metadata);
  try {
    const result = syncFn();
    return result;
  } finally {
    performanceMonitor.endTiming(name);
  }
};

// React hook for measuring component render time
import { useEffect, useRef } from 'react';

export const useRenderTime = (componentName: string) => {
  const renderStartTime = useRef<number>(performance.now());

  useEffect(() => {
    const renderTime = performance.now() - renderStartTime.current;
    
    // Log slow renders (> 16ms for 60fps)
    if (renderTime > 16) {
      console.warn(`Slow render: ${componentName} took ${renderTime.toFixed(2)}ms`);
    }
    
    // Reset for next render
    renderStartTime.current = performance.now();
  });
};

// Bundle size analyzer
export const bundleAnalyzer = {
  // Estimate component size impact
  estimateComponentSize: (componentName: string): void => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      const entries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const jsEntries = entries.filter(entry => 
        entry.name.includes('.js') && 
        entry.name.includes(componentName.toLowerCase())
      );
      
      if (jsEntries.length > 0) {
        const totalSize = jsEntries.reduce((sum, entry) => sum + (entry.transferSize || 0), 0);
        console.log(`${componentName} estimated bundle impact: ${(totalSize / 1024).toFixed(2)}KB`);
      }
    }
  },

  // Get total bundle size
  getTotalBundleSize: (): number => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      const entries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const jsEntries = entries.filter(entry => entry.name.includes('.js'));
      return jsEntries.reduce((sum, entry) => sum + (entry.transferSize || 0), 0);
    }
    return 0;
  }
};

// Memory usage monitoring
export const memoryMonitor = {
  getCurrentUsage: (): any => {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1048576), // MB
        total: Math.round(memory.totalJSHeapSize / 1048576), // MB
        limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
      };
    }
    return null;
  },

  logMemoryUsage: (label?: string): void => {
    const usage = memoryMonitor.getCurrentUsage();
    if (usage) {
      console.log(`Memory Usage${label ? ` (${label})` : ''}:`, 
        `${usage.used}MB / ${usage.total}MB (limit: ${usage.limit}MB)`);
    }
  },

  // Check for memory leaks
  checkForLeaks: (): boolean => {
    const usage = memoryMonitor.getCurrentUsage();
    if (usage && usage.used > usage.limit * 0.8) {
      console.warn('Potential memory leak detected: using >80% of heap limit');
      return true;
    }
    return false;
  }
};

// Web Vitals measurement
export const webVitals = {
  measureCLS: (): void => {
    if (typeof window !== 'undefined') {
      let clsValue = 0;
      let clsEntries: any[] = [];

      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += (entry as any).value;
            clsEntries.push(entry);
          }
        }
        console.log('CLS:', clsValue);
      });

      observer.observe({ entryTypes: ['layout-shift'] });
    }
  },

  measureFCP: (): void => {
    if (typeof window !== 'undefined') {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name === 'first-contentful-paint') {
            console.log('FCP:', entry.startTime);
          }
        }
      });

      observer.observe({ entryTypes: ['paint'] });
    }
  }
};

// Initialize web vitals measurement
if (typeof window !== 'undefined') {
  webVitals.measureCLS();
  webVitals.measureFCP();
}

export default performanceMonitor;
