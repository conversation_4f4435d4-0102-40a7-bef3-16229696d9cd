
import { useMemo, type FC } from 'react';
import type { Product } from './types';
import { stores, reviews as allReviews } from './data';
import { useWishlist } from './context/WishlistContext';
import { useComparison } from './context/ComparisonContext';
import Icon from './Icon';
import StarRating from './components/StarRating';

interface ProductCardProps {
    product: Product;
    onSelect: (id: string) => void;
}

const getDiscountedPrice = (price: number, discountPercentage?: number): number => {
    if (!discountPercentage) return price;
    return price * (1 - discountPercentage / 100);
};

const ProductCard: FC<ProductCardProps> = ({ product, onSelect }) => {
    const store = stores.find(s => s.id === product.storeId);
    const discountedPrice = getDiscountedPrice(product.basePrice, product.offer?.discountPercentage);
    const { isWishlisted, addToWishlist, removeFromWishlist } = useWishlist();
    const { isInComparison, addToComparison, removeFromComparison, comparisonCount, maxComparisonItems } = useComparison();

    const isProductWishlisted = isWishlisted(product.id);
    const isProductInComparison = isInComparison(product.id);

    const handleWishlistToggle = (e: React.MouseEvent) => {
        e.stopPropagation(); // Prevent card's onSelect from firing
        if (isProductWishlisted) {
            removeFromWishlist(product.id);
        } else {
            addToWishlist(product.id);
        }
    };

    const handleComparisonToggle = (e: React.MouseEvent) => {
        e.stopPropagation(); // Prevent card's onSelect from firing
        if (isProductInComparison) {
            removeFromComparison(product.id);
        } else {
            addToComparison(product.id);
        }
    };
    
    const reviewInfo = useMemo(() => {
        const productReviews = allReviews.filter(r => r.productId === product.id);
        if (productReviews.length === 0) {
            return { averageRating: 0, reviewCount: 0 };
        }
        const totalRating = productReviews.reduce((acc, review) => acc + review.rating, 0);
        return {
            averageRating: totalRating / productReviews.length,
            reviewCount: productReviews.length
        };
    }, [product.id]);

    return (
        <div 
            className="group relative flex flex-col overflow-hidden rounded-lg border border-gray-200 bg-white hover:shadow-xl transition-shadow duration-300 cursor-pointer"
            onClick={() => onSelect(product.id)}
        >
            <div className="aspect-w-3 aspect-h-4 bg-gray-200 sm:aspect-none sm:h-96">
                <img
                    src={product.variants[0].imageUrl}
                    alt={product.name}
                    className="h-full w-full object-cover object-center sm:h-full sm:w-full transition-transform duration-300 group-hover:scale-105"
                />
            </div>
            {product.offer && (
                <div className="absolute top-3 left-3 bg-red-600 text-white text-xs font-bold px-2 py-1 rounded-md z-10">
                    -{product.offer.discountPercentage}%
                </div>
            )}
            <div className="absolute top-3 right-3 z-10 flex flex-col gap-2">
                <button
                    onClick={handleWishlistToggle}
                    className="p-1.5 bg-white/60 backdrop-blur-sm rounded-full text-gray-700 hover:text-red-500 transition-colors duration-200"
                    aria-label={isProductWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
                >
                    <Icon name="heart" className={`w-6 h-6 transition-all ${isProductWishlisted ? 'text-red-500 fill-current' : ''}`} />
                </button>
                <button
                    onClick={handleComparisonToggle}
                    className={`p-1.5 bg-white/60 backdrop-blur-sm rounded-full transition-colors duration-200 ${
                        isProductInComparison
                            ? 'text-indigo-600 bg-indigo-50/80'
                            : 'text-gray-700 hover:text-indigo-600'
                    } ${comparisonCount >= maxComparisonItems && !isProductInComparison ? 'opacity-50 cursor-not-allowed' : ''}`}
                    aria-label={isProductInComparison ? 'Remove from comparison' : 'Add to comparison'}
                    disabled={comparisonCount >= maxComparisonItems && !isProductInComparison}
                >
                    <Icon name="scale" className="w-6 h-6" />
                </button>
            </div>
            <div className="flex flex-1 flex-col space-y-2 p-4">
                <h3 className="text-lg font-bold text-gray-900">
                    <span aria-hidden="true" className="absolute inset-0" />
                    {product.name}
                </h3>
                <p className="text-sm text-gray-500">{store?.name || 'Top Brand'}</p>
                 {reviewInfo.reviewCount > 0 && (
                    <div className="flex items-center">
                        <StarRating rating={reviewInfo.averageRating} size="sm" />
                        <p className="ml-1 text-xs text-gray-500">({reviewInfo.reviewCount})</p>
                    </div>
                )}
                <div className="flex flex-1 flex-col justify-end pt-2">
                    <div className="flex items-baseline space-x-2">
                        <p className="text-xl font-bold text-gray-900">${discountedPrice.toFixed(2)}</p>
                        {product.offer && (
                            <p className="text-md font-medium text-gray-500 line-through">${product.basePrice.toFixed(2)}</p>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ProductCard;