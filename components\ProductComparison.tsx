import { useMemo, type FC } from 'react';
import { useComparison } from '../context/ComparisonContext';
import { products as allProducts, stores, reviews as allReviews } from '../data';
import type { Product } from '../types';
import Icon from './Icon';
import StarRating from './StarRating';

interface ProductComparisonProps {
  isOpen: boolean;
  onClose: () => void;
  onProductSelect: (id: string) => void;
}

const getDiscountedPrice = (price: number, discountPercentage?: number): number => {
  if (!discountPercentage) return price;
  return price * (1 - discountPercentage / 100);
};

const ProductComparison: FC<ProductComparisonProps> = ({ isOpen, onClose, onProductSelect }) => {
  const { comparisonItems, removeFromComparison, clearComparison } = useComparison();

  const comparisonProducts = useMemo(() => {
    return comparisonItems.map(item => {
      const product = allProducts.find(p => p.id === item.productId);
      if (!product) return null;
      
      const store = stores.find(s => s.id === product.storeId);
      const productReviews = allReviews.filter(r => r.productId === product.id);
      const averageRating = productReviews.length > 0 
        ? productReviews.reduce((acc, review) => acc + review.rating, 0) / productReviews.length 
        : 0;
      
      return {
        ...product,
        store,
        averageRating,
        reviewCount: productReviews.length,
        discountedPrice: getDiscountedPrice(product.basePrice, product.offer?.discountPercentage)
      };
    }).filter(Boolean) as Array<Product & { 
      store: typeof stores[0] | undefined; 
      averageRating: number; 
      reviewCount: number; 
      discountedPrice: number; 
    }>;
  }, [comparisonItems]);

  const handleProductClick = (productId: string) => {
    onProductSelect(productId);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      <div className="absolute inset-x-0 bottom-0 bg-white rounded-t-2xl shadow-2xl max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">
            Product Comparison ({comparisonProducts.length})
          </h2>
          <div className="flex items-center gap-3">
            {comparisonProducts.length > 0 && (
              <button
                onClick={clearComparison}
                className="text-sm text-red-600 hover:text-red-700 font-medium"
              >
                Clear All
              </button>
            )}
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
            >
              <Icon name="x-mark" className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="p-6 overflow-auto max-h-[calc(90vh-100px)]">
          {comparisonProducts.length === 0 ? (
            <div className="text-center py-20">
              <Icon name="scale" className="w-16 h-16 mx-auto text-gray-300" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">No products to compare</h3>
              <p className="mt-2 text-sm text-gray-500">
                Add products to comparison by clicking the compare icon on product cards.
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr>
                    <td className="p-4 font-medium text-gray-900 border-b border-gray-200 w-32">
                      Product
                    </td>
                    {comparisonProducts.map((product) => (
                      <td key={product.id} className="p-4 border-b border-gray-200 min-w-64">
                        <div className="text-center">
                          <img
                            src={product.variants[0].imageUrl}
                            alt={product.name}
                            className="w-32 h-40 object-cover rounded-lg mx-auto mb-3 cursor-pointer hover:opacity-80 transition-opacity"
                            onClick={() => handleProductClick(product.id)}
                          />
                          <h3 
                            className="font-semibold text-gray-900 cursor-pointer hover:text-indigo-600 transition-colors"
                            onClick={() => handleProductClick(product.id)}
                          >
                            {product.name}
                          </h3>
                          <button
                            onClick={() => removeFromComparison(product.id)}
                            className="mt-2 text-xs text-red-600 hover:text-red-700"
                          >
                            Remove
                          </button>
                        </div>
                      </td>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="p-4 font-medium text-gray-900 border-b border-gray-200">Price</td>
                    {comparisonProducts.map((product) => (
                      <td key={product.id} className="p-4 border-b border-gray-200 text-center">
                        <div className="space-y-1">
                          <p className="text-xl font-bold text-gray-900">
                            ${product.discountedPrice.toFixed(2)}
                          </p>
                          {product.offer && (
                            <p className="text-sm text-gray-500 line-through">
                              ${product.basePrice.toFixed(2)}
                            </p>
                          )}
                          {product.offer && (
                            <span className="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                              -{product.offer.discountPercentage}% OFF
                            </span>
                          )}
                        </div>
                      </td>
                    ))}
                  </tr>
                  <tr>
                    <td className="p-4 font-medium text-gray-900 border-b border-gray-200">Store</td>
                    {comparisonProducts.map((product) => (
                      <td key={product.id} className="p-4 border-b border-gray-200 text-center">
                        <p className="text-sm text-gray-600">{product.store?.name || 'Unknown'}</p>
                      </td>
                    ))}
                  </tr>
                  <tr>
                    <td className="p-4 font-medium text-gray-900 border-b border-gray-200">Rating</td>
                    {comparisonProducts.map((product) => (
                      <td key={product.id} className="p-4 border-b border-gray-200 text-center">
                        {product.reviewCount > 0 ? (
                          <div className="flex flex-col items-center gap-1">
                            <StarRating rating={product.averageRating} size="sm" />
                            <p className="text-xs text-gray-500">({product.reviewCount} reviews)</p>
                          </div>
                        ) : (
                          <p className="text-sm text-gray-400">No reviews</p>
                        )}
                      </td>
                    ))}
                  </tr>
                  <tr>
                    <td className="p-4 font-medium text-gray-900 border-b border-gray-200">Category</td>
                    {comparisonProducts.map((product) => (
                      <td key={product.id} className="p-4 border-b border-gray-200 text-center">
                        <span className="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                          {product.category}
                        </span>
                      </td>
                    ))}
                  </tr>
                  <tr>
                    <td className="p-4 font-medium text-gray-900 border-b border-gray-200">Available Colors</td>
                    {comparisonProducts.map((product) => (
                      <td key={product.id} className="p-4 border-b border-gray-200 text-center">
                        <div className="flex justify-center gap-1 flex-wrap">
                          {[...new Map(product.variants.map(v => [v.color, v])).values()].map((variant) => (
                            <div
                              key={variant.id}
                              className="w-6 h-6 rounded-full border-2 border-gray-300"
                              style={{ backgroundColor: variant.colorHex }}
                              title={variant.color}
                            />
                          ))}
                        </div>
                      </td>
                    ))}
                  </tr>
                  <tr>
                    <td className="p-4 font-medium text-gray-900">Available Sizes</td>
                    {comparisonProducts.map((product) => (
                      <td key={product.id} className="p-4 text-center">
                        <div className="flex justify-center gap-1 flex-wrap">
                          {[...new Set(product.variants.map(v => v.size))].map((size) => (
                            <span
                              key={size}
                              className="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded"
                            >
                              {size}
                            </span>
                          ))}
                        </div>
                      </td>
                    ))}
                  </tr>
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductComparison;
