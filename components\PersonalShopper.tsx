
import { useState, useEffect, useRef, useCallback, Fragment, type FC, type FormEvent } from 'react';
import { GoogleGenAI, type Chat } from "@google/genai";
import type { ChatMessage, Product } from '../types';
import { products as allProducts } from '../data';
import Icon from './Icon';

interface PersonalShopperProps {
    isOpen: boolean;
    onClose: () => void;
    onProductSelect: (id: string) => void;
}

const RecommendedProduct: FC<{productId: string, onProductSelect: (id: string) => void}> = ({ productId, onProductSelect }) => {
    const product = allProducts.find(p => p.id === productId);
    if (!product) return <div className="text-sm text-red-500 my-2">[Error: Recommended product not found]</div>;

    return (
        <div 
            onClick={() => onProductSelect(product.id)}
            className="flex items-center gap-3 my-2 p-2 rounded-lg bg-white hover:bg-indigo-50 border border-gray-200 cursor-pointer transition-colors duration-200"
        >
            <img src={product.variants[0].imageUrl} alt={product.name} className="w-16 h-16 rounded-md object-cover" />
            <div className="flex-1">
                <p className="font-semibold text-gray-800 text-sm">{product.name}</p>
                <p className="text-gray-600 text-sm">${product.basePrice.toFixed(2)}</p>
            </div>
        </div>
    );
};

const TypingIndicator: FC = () => (
    <div className="flex items-center space-x-1.5 py-2 px-3">
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0s'}}></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
    </div>
);

const PersonalShopper: FC<PersonalShopperProps> = ({ isOpen, onClose, onProductSelect }) => {
    const [messages, setMessages] = useState<ChatMessage[]>([]);
    const [userInput, setUserInput] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const chatRef = useRef<Chat | null>(null);
    const messagesEndRef = useRef<HTMLDivElement>(null);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(scrollToBottom, [messages, isLoading]);

    useEffect(() => {
        if (isOpen) {
            const initChat = () => {
                try {
                    setError(null);
                    const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });
                    const availableProducts = allProducts.map(({ id, name, category, description, tags }) => ({ id, name, category, description, tags }));
                    
                    const systemInstruction = `You are "Marcat Style AI", a friendly and expert personal shopper for Marcat, a men's clothing marketplace. Your goal is to help users find the perfect items and create stylish outfits. You must be conversational, helpful, and embody the sophisticated yet approachable tone of the Marcat brand. When a user asks for recommendations, you MUST use the provided product list. Do not invent products.
                    **Product Catalog:** ${JSON.stringify(availableProducts)}
                    **Recommendation Rules:**
                    1. Analyze the user's request (e.g., occasion, style preference, color, item type).
                    2. Select 1 to 3 suitable products from the catalog.
                    3. In your response, first provide some styling advice or commentary in a friendly tone.
                    4. To embed a product recommendation in your response, you MUST use the exact format: [PRODUCT:product_id]. For example, for the "Slim-Fit Chinos", you would write [PRODUCT:prod-5].
                    5. Do not include the product name or price in the text around the [PRODUCT:...] tag, as the UI will render a card for it.
                    Always start the first message of the conversation with: "Hello! I'm the Marcat Style AI, your personal shopper. Whether you're looking for a specific item or need some style inspiration, I'm here to help. What's on your mind today?"`;

                    chatRef.current = ai.chats.create({ model: 'gemini-2.5-flash-preview-04-17', config: { systemInstruction }});

                    setMessages([{
                        id: 'init',
                        role: 'model',
                        text: "Hello! I'm the Marcat Style AI, your personal shopper. Whether you're looking for a specific item or need some style inspiration, I'm here to help. What's on your mind today?"
                    }]);

                } catch (e) {
                    console.error("Initialization error:", e);
                    setError("Could not initialize the Style AI. Please check your configuration.");
                }
            };
            initChat();
        } else {
            // Reset state when closed
            setMessages([]);
            setUserInput('');
            setIsLoading(false);
            setError(null);
            chatRef.current = null;
        }
    }, [isOpen]);

    const handleSendMessage = async (e: FormEvent) => {
        e.preventDefault();
        if (!userInput.trim() || isLoading || !chatRef.current) return;

        const text = userInput;
        setUserInput('');
        setMessages(prev => [...prev, { id: Date.now().toString(), role: 'user', text }]);
        setIsLoading(true);
        setError(null);
        
        try {
            const stream = await chatRef.current.sendMessageStream({ message: text });
            
            let modelResponse = '';
            const modelMessageId = `model-${Date.now()}`;
            
            // Add a placeholder for the model's response
            setMessages(prev => [...prev, { id: modelMessageId, role: 'model', text: '' }]);

            for await (const chunk of stream) {
                modelResponse += chunk.text;
                setMessages(prev => prev.map(msg => msg.id === modelMessageId ? {...msg, text: modelResponse} : msg));
            }
        } catch (e) {
            console.error("Error sending message:", e);
            setError("Sorry, I'm having trouble connecting. Please try again in a moment.");
        } finally {
            setIsLoading(false);
        }
    };
    
    const renderMessageContent = (text: string) => {
        const parts = text.split(/(\[PRODUCT:.*?\])/g).filter(Boolean);
        return parts.map((part, index) => {
            const match = part.match(/\[PRODUCT:(.*?)\]/);
            if (match) {
                const productId = match[1];
                return <RecommendedProduct key={`${productId}-${index}`} productId={productId} onProductSelect={onProductSelect} />;
            }
            return <Fragment key={index}>{part}</Fragment>;
        });
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-end justify-center sm:justify-end p-0 sm:p-4" aria-labelledby="slide-over-title" role="dialog" aria-modal="true">
            <div onClick={onClose} className="fixed inset-0 bg-gray-900 bg-opacity-60 transition-opacity"></div>
            
            <div className="relative w-full h-full sm:h-[calc(100%-2rem)] sm:w-full sm:max-w-md bg-white rounded-t-2xl sm:rounded-2xl shadow-2xl flex flex-col transform transition-transform ease-out duration-300">
                {/* Header */}
                <header className="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
                    <div className="flex items-center gap-3">
                         <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                            <Icon name="sparkles" className="w-6 h-6 text-indigo-600"/>
                        </div>
                        <h2 className="text-lg font-semibold text-gray-900" id="slide-over-title">AI Personal Shopper</h2>
                    </div>
                    <button type="button" className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100" onClick={onClose}>
                        <Icon name="close" className="h-6 w-6"/>
                    </button>
                </header>

                {/* Message List */}
                <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50/50">
                    {messages.map((message) => (
                        <div key={message.id} className={`flex items-end gap-2 ${message.role === 'user' ? 'justify-end' : ''}`}>
                             {message.role === 'model' && (
                                <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                                    <Icon name="sparkles" className="w-5 h-5 text-indigo-500"/>
                                </div>
                            )}
                            <div className={`max-w-xs md:max-w-sm rounded-2xl px-4 py-2.5 text-sm ${message.role === 'user' ? 'bg-indigo-600 text-white rounded-br-lg' : 'bg-gray-200 text-gray-800 rounded-bl-lg'}`}>
                                {renderMessageContent(message.text)}
                            </div>
                        </div>
                    ))}
                    {isLoading && (
                        <div className="flex items-end gap-2">
                             <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                                <Icon name="sparkles" className="w-5 h-5 text-indigo-500"/>
                            </div>
                            <div className="bg-gray-200 rounded-2xl rounded-bl-lg">
                                <TypingIndicator />
                            </div>
                        </div>
                    )}
                     {error && (
                        <div className="p-3 bg-red-100 text-red-700 rounded-lg text-sm">
                            {error}
                        </div>
                    )}
                    <div ref={messagesEndRef} />
                </div>

                {/* Input Form */}
                <footer className="p-4 border-t border-gray-200 bg-white flex-shrink-0">
                    <form onSubmit={handleSendMessage} className="flex items-center gap-2">
                        <input
                            type="text"
                            value={userInput}
                            onChange={(e) => setUserInput(e.target.value)}
                            placeholder="Ask for style advice..."
                            className="flex-1 w-full px-4 py-2.5 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-indigo-500"
                            disabled={isLoading}
                        />
                        <button
                            type="submit"
                            disabled={isLoading || !userInput.trim()}
                            className="flex-shrink-0 w-11 h-11 flex items-center justify-center bg-indigo-600 text-white rounded-full transition-colors duration-200 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
                        >
                            <Icon name="paper-airplane" className="w-5 h-5 -ml-px -mt-px"/>
                        </button>
                    </form>
                </footer>
            </div>
        </div>
    );
};

export default PersonalShopper;
