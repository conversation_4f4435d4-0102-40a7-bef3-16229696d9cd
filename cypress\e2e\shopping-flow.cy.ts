describe('Marcat E-commerce Shopping Flow', () => {
  beforeEach(() => {
    cy.visit('/');
    cy.viewport(1280, 720);
  });

  it('should complete full shopping journey', () => {
    // 1. Browse products
    cy.get('[data-testid="product-list"]').should('be.visible');
    cy.get('[data-testid="product-card"]').should('have.length.greaterThan', 0);

    // 2. Search for products
    cy.get('[data-testid="search-input"]').type('shirt');
    cy.get('[data-testid="search-results"]').should('contain', 'Found');
    
    // 3. Filter by category
    cy.get('[data-testid="category-filter"]').contains('Shirts').click();
    cy.get('[data-testid="product-card"]').should('contain', 'Shirt');

    // 4. Select a product
    cy.get('[data-testid="product-card"]').first().click();
    cy.get('[data-testid="product-detail"]').should('be.visible');

    // 5. Add to cart
    cy.get('[data-testid="add-to-cart-btn"]').click();
    cy.get('[data-testid="cart-modal"]').should('be.visible');
    cy.get('[data-testid="cart-item"]').should('have.length', 1);

    // 6. Proceed to checkout
    cy.get('[data-testid="checkout-btn"]').click();
    cy.get('[data-testid="checkout-form"]').should('be.visible');

    // 7. Fill checkout form
    cy.get('[data-testid="shipping-name"]').type('John Doe');
    cy.get('[data-testid="shipping-email"]').type('<EMAIL>');
    cy.get('[data-testid="shipping-address"]').type('123 Main St');
    cy.get('[data-testid="shipping-city"]').type('New York');
    cy.get('[data-testid="shipping-zip"]').type('10001');

    // 8. Complete order
    cy.get('[data-testid="place-order-btn"]').click();
    cy.get('[data-testid="order-success"]').should('be.visible');
    cy.get('[data-testid="order-number"]').should('contain', 'Order #');
  });

  it('should handle product comparison', () => {
    // Add products to comparison
    cy.get('[data-testid="compare-btn"]').first().click();
    cy.get('[data-testid="compare-btn"]').eq(1).click();

    // Open comparison
    cy.get('[data-testid="comparison-fab"]').should('be.visible').click();
    cy.get('[data-testid="comparison-modal"]').should('be.visible');
    cy.get('[data-testid="comparison-product"]').should('have.length', 2);

    // Remove from comparison
    cy.get('[data-testid="remove-comparison-btn"]').first().click();
    cy.get('[data-testid="comparison-product"]').should('have.length', 1);
  });

  it('should handle wishlist functionality', () => {
    // Add to wishlist
    cy.get('[data-testid="wishlist-btn"]').first().click();
    cy.get('[data-testid="wishlist-success"]').should('be.visible');

    // Open wishlist
    cy.get('[data-testid="wishlist-fab"]').click();
    cy.get('[data-testid="wishlist-modal"]').should('be.visible');
    cy.get('[data-testid="wishlist-item"]').should('have.length', 1);

    // Remove from wishlist
    cy.get('[data-testid="remove-wishlist-btn"]').click();
    cy.get('[data-testid="wishlist-empty"]').should('be.visible');
  });

  it('should handle user authentication', () => {
    // Open login modal
    cy.get('[data-testid="login-btn"]').click();
    cy.get('[data-testid="auth-modal"]').should('be.visible');

    // Fill login form
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="password-input"]').type('password123');
    cy.get('[data-testid="login-submit"]').click();

    // Should show success or error
    cy.get('[data-testid="auth-message"]').should('be.visible');
  });

  it('should handle personal shopper', () => {
    // Open personal shopper
    cy.get('[data-testid="personal-shopper-fab"]').click();
    cy.get('[data-testid="personal-shopper-modal"]').should('be.visible');

    // Send message
    cy.get('[data-testid="chat-input"]').type('I need help finding a shirt');
    cy.get('[data-testid="send-message-btn"]').click();
    cy.get('[data-testid="chat-message"]').should('contain', 'I need help');

    // Should get AI response
    cy.get('[data-testid="ai-response"]', { timeout: 10000 }).should('be.visible');
  });

  it('should handle responsive design', () => {
    // Test mobile viewport
    cy.viewport(375, 667);
    cy.get('[data-testid="mobile-menu-btn"]').should('be.visible');
    cy.get('[data-testid="product-grid"]').should('have.class', 'grid-cols-1');

    // Test tablet viewport
    cy.viewport(768, 1024);
    cy.get('[data-testid="product-grid"]').should('have.class', 'sm:grid-cols-2');

    // Test desktop viewport
    cy.viewport(1280, 720);
    cy.get('[data-testid="product-grid"]').should('have.class', 'lg:grid-cols-3');
  });

  it('should handle error states', () => {
    // Test network error handling
    cy.intercept('GET', '/api/products', { forceNetworkError: true });
    cy.reload();
    cy.get('[data-testid="error-message"]').should('be.visible');
    cy.get('[data-testid="retry-btn"]').should('be.visible');
  });

  it('should handle performance requirements', () => {
    // Test page load performance
    cy.visit('/', {
      onBeforeLoad: (win) => {
        win.performance.mark('start');
      },
      onLoad: (win) => {
        win.performance.mark('end');
        win.performance.measure('pageLoad', 'start', 'end');
        const measure = win.performance.getEntriesByName('pageLoad')[0];
        expect(measure.duration).to.be.lessThan(3000); // 3 seconds
      }
    });

    // Test lazy loading
    cy.get('[data-testid="product-card"]').should('be.visible');
    cy.scrollTo('bottom');
    cy.get('[data-testid="lazy-image"]').should('be.visible');
  });

  it('should handle accessibility requirements', () => {
    // Test keyboard navigation
    cy.get('body').tab();
    cy.focused().should('have.attr', 'data-testid', 'search-input');
    
    cy.tab();
    cy.focused().should('have.attr', 'data-testid', 'category-filter');

    // Test ARIA labels
    cy.get('[data-testid="add-to-cart-btn"]').should('have.attr', 'aria-label');
    cy.get('[data-testid="wishlist-btn"]').should('have.attr', 'aria-label');

    // Test focus management
    cy.get('[data-testid="product-card"]').first().click();
    cy.get('[data-testid="product-detail"]').should('have.focus');
  });

  it('should handle admin functionality', () => {
    // Login as admin
    cy.get('[data-testid="login-btn"]').click();
    cy.get('[data-testid="email-input"]').type(Cypress.env('adminUser').email);
    cy.get('[data-testid="password-input"]').type(Cypress.env('adminUser').password);
    cy.get('[data-testid="login-submit"]').click();

    // Access admin dashboard
    cy.get('[data-testid="admin-btn"]').click();
    cy.get('[data-testid="admin-dashboard"]').should('be.visible');

    // Test admin features
    cy.get('[data-testid="admin-products"]').click();
    cy.get('[data-testid="product-management"]').should('be.visible');

    cy.get('[data-testid="admin-orders"]').click();
    cy.get('[data-testid="order-management"]').should('be.visible');

    cy.get('[data-testid="admin-users"]').click();
    cy.get('[data-testid="user-management"]').should('be.visible');

    cy.get('[data-testid="admin-analytics"]').click();
    cy.get('[data-testid="analytics-dashboard"]').should('be.visible');
  });
});
